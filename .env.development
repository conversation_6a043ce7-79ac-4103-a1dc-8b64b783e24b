# XUI App Server - 开发环境配置模板
# 复制此文件为 .env.development 并根据需要修改

# =============================================================================
# 基础配置
# =============================================================================
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# =============================================================================
# 数据库配置 (PostgreSQL)
# =============================================================================
DB_HOST=*************
# DB_HOST=***********
DB_PORT=5432
DB_NAME=xui
DB_USER=ai
DB_PASSWORD=WyaJCZZb93AvlL6RA7r8
DB_SSL=false
DB_MAX_CONNECTIONS=10
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=2000

# =============================================================================
# 安全配置
# =============================================================================
# 注意：本项目使用网关认证，通过 userId header 传递用户信息
BCRYPT_ROUNDS=8
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# =============================================================================
# CORS 配置
# =============================================================================
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=debug # 开发环境推荐使用 debug 级别
LOG_FORMAT=dev  # 开发环境推荐使用 dev 格式
ENABLE_FILE_LOGGING=true    # 开发环境推荐启用文件日志

# =============================================================================
# Langfuse 配置 (LLM 可观测性) - 开发环境
# =============================================================================
LANGFUSE_ENABLED=true
LANGFUSE_PUBLIC_KEY=pk-lf-7ec26f80-c670-4130-a149-4ba38a4f1313
LANGFUSE_SECRET_KEY=******************************************
LANGFUSE_HOST=https://langfuse.yxt.com.cn/
LANGFUSE_ENVIRONMENT=development
LANGFUSE_RELEASE=dev
LANGFUSE_DEBUG=true
LANGFUSE_TIMEOUT=10000
LANGFUSE_MAX_RETRIES=2
LANGFUSE_SAMPLE_RATE=1.0
LANGFUSE_FLUSH_INTERVAL=1000

# =============================================================================
# Redis 配置 (用于 SSE 会话管理和消息缓存)
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000
REDIS_CONNECTION_TIMEOUT=5000
REDIS_COMMAND_TIMEOUT=5000
REDIS_KEY_PREFIX=xui:
REDIS_SESSION_TTL=3600
REDIS_MESSAGE_CACHE_SIZE=100

# =============================================================================
# 注意事项
# =============================================================================
# 1. 开发环境配置不应用于生产环境
# 2. 密码可以使用简单值，但不要提交到版本控制
# 3. 确保本地数据库服务正在运行
# 4. 使用 pnpm dev:setup 快速设置开发环境
# 5. 确保 Redis 服务正在运行（用于 SSE 会话管理）
