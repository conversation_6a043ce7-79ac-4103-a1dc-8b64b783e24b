#!/bin/bash

# XUI App Server - 快速部署脚本 (PM2 模式)
# 用于快速设置和使用 PM2 部署应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
XUI App Server 快速部署脚本 (PM2 模式)

用法: $0 [选项] <环境>

环境:
  dev         开发环境部署
  staging     预发布环境部署
  production  生产环境部署

选项:
  --setup-only        仅设置环境，不启动服务
  --skip-deps         跳过依赖安装
  --skip-build        跳过构建步骤
  --skip-db           跳过数据库操作
  --skip-validation   跳过环境配置验证
  --stop-only         仅停止现有服务，不重新部署
  --force-stop        强制停止所有相关服务 (同 --stop-only)
  --help              显示此帮助信息

示例:
  $0 dev                    # 使用 PM2 部署到开发环境
  $0 production             # 使用 PM2 部署到生产环境
  $0 dev --setup-only       # 仅设置开发环境
  $0 --stop-only            # 停止所有 PM2 服务
EOF
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装。请安装 Node.js 20.0.0 或更高版本"
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2)
    local required_version="20.0.0"
    
    if ! printf '%s\n%s\n' "$required_version" "$node_version" | sort -V -C; then
        log_error "Node.js 版本过低。当前版本: $node_version，要求: $required_version+"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm 未安装，正在安装..."
        npm install -g pnpm
    fi
    
    # 检查 PostgreSQL (如果不跳过数据库)
    if [ "$SKIP_DB" != "true" ]; then
        if ! command -v psql &> /dev/null; then
            log_warning "PostgreSQL 客户端未安装，某些数据库操作可能无法执行"
        fi
    fi

    # 检查 tsx (TypeScript 执行器)
    if ! command -v tsx &> /dev/null; then
        log_info "tsx 未全局安装，将使用 npx tsx"
    fi
    
    log_success "系统要求检查完成"
}

# 设置环境
setup_environment() {
    local env=$1
    log_info "设置 $env 环境..."

    # 映射环境名称到实际的配置文件名
    local config_env=""
    case $env in
        "dev")
            config_env="development"
            ;;
        "prod")
            config_env="production"
            ;;
        *)
            config_env="$env"
            ;;
    esac

    # 复制环境配置文件
    if [ ! -f ".env.$config_env" ]; then
        if [ -f ".env.$config_env.example" ]; then
            cp ".env.$config_env.example" ".env.$config_env"
            log_success "已创建 .env.$config_env 文件"
            log_warning "请编辑 .env.$config_env 文件并填入正确的配置值"
        else
            log_error "环境配置模板 .env.$config_env.example 不存在"
            exit 1
        fi
    else
        log_info ".env.$config_env 文件已存在"
    fi

    # 检查是否存在 .env.local 文件（本地覆盖配置）
    if [ -f ".env.local" ]; then
        log_info "检测到 .env.local 文件，将覆盖 .env.$config_env 中的配置"
        log_warning "确保 .env.local 文件格式正确且包含所需的配置"
    fi
    
    # 设置 NODE_ENV
    export NODE_ENV=$env
    
    # 创建必要的目录
    mkdir -p logs pids data
    
    log_success "环境设置完成"
}

# 验证环境配置
validate_environment() {
    local env=$1
    log_info "验证环境配置..."

    # 检查是否存在 node_modules，如果不存在则跳过验证
    if [ ! -d "node_modules" ]; then
        log_warning "依赖包未安装，跳过环境配置验证"
        log_info "请在安装依赖后手动验证环境配置"
        return 0
    fi

    # 映射环境名称
    local node_env=""
    case $env in
        "dev")
            node_env="development"
            ;;
        "prod")
            node_env="production"
            ;;
        *)
            node_env="$env"
            ;;
    esac

    # 设置临时环境变量
    export NODE_ENV=$node_env

    # 使用 tsx 运行配置验证
    if command -v tsx &> /dev/null; then
        tsx_cmd="tsx"
    else
        tsx_cmd="npx tsx"
    fi

    if $tsx_cmd scripts/validate-env.ts; then
        log_success "环境配置验证通过"
    else
        log_error "环境配置验证失败，请检查配置文件"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    if [ "$SKIP_DEPS" = "true" ]; then
        log_info "跳过依赖安装"
        return
    fi
    
    log_info "安装依赖..."
    pnpm install
    log_success "依赖安装完成"
}

# 构建应用
build_application() {
    if [ "$SKIP_BUILD" = "true" ]; then
        log_info "跳过构建步骤"
        return
    fi
    
    log_info "构建应用..."
    pnpm build
    log_success "应用构建完成"
}

# 数据库操作
setup_database() {
    if [ "$SKIP_DB" = "true" ]; then
        log_info "跳过数据库操作"
        return
    fi
    
    log_info "设置数据库..."
    
    # 运行数据库设置和迁移
    if [ -f "package.json" ] && grep -q "db:setup" package.json; then
        # 使用 db:setup 脚本，它会处理数据库创建和迁移
        pnpm db:setup
        log_success "数据库设置和迁移完成"
    elif [ -f "package.json" ] && grep -q "db:migrate" package.json; then
        # 回退到仅迁移
        pnpm db:migrate
        log_success "数据库迁移完成"
    else
        log_warning "未找到数据库设置或迁移脚本"
    fi
    
    # 注意：根据用户记忆，已移除种子数据功能
    # 不再提供种子数据导入选项
}

# 停止 PM2 服务
stop_pm2_service() {
    log_info "停止 PM2 服务..."

    # 检查 PM2 是否安装
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 未安装，无法停止服务"
        return
    fi
    
    # 尝试停止并删除现有的进程，即使它不存在也不会报错
    log_info "正在清理旧的 'xui-app-server' PM2 进程（如果存在）..."
    pm2 stop xui-app-server --silent || true
    pm2 delete xui-app-server --silent || true
    log_success "旧进程清理完成。"
}

# PM2 部署
deploy_pm2() {
    log_info "使用 PM2 部署..."

    # 检查 PM2 是否安装
    if ! command -v pm2 &> /dev/null; then
        log_info "安装 PM2..."
        npm install -g pm2
    fi

    # 停止现有应用
    stop_pm2_service

    # 映射环境名称
    local pm2_env=""
    case $ENVIRONMENT in
        "dev")
            pm2_env="development"
            ;;
        "prod")
            pm2_env="production"
            ;;
        *)
            pm2_env="$ENVIRONMENT"
            ;;
    esac

    # 启动应用
    log_info "启动 PM2 应用..."
    pm2 start ecosystem.config.cjs --env $pm2_env
    pm2 save

    log_success "PM2 部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        # 尝试简单的 ping 端点，更快更可靠
        if curl -f http://localhost:3000/api/health/ping >/dev/null 2>&1; then
            log_success "健康检查通过"
            return 0
        fi

        log_info "健康检查失败，重试 $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    log_error "健康检查失败"
    return 1
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "环境: $ENVIRONMENT"
    echo "部署方式: PM2"
    echo "应用地址: http://localhost:3000"
    echo "健康检查: http://localhost:3000/api/health/ping"
    echo "详细健康检查: http://localhost:3000/api/health"
    echo
    
    echo "PM2 管理命令:"
    echo "  查看状态: pm2 status"
    echo "  查看日志: pm2 logs xui-app-server"
    echo "  重启应用: pm2 restart xui-app-server"
    echo "  停止应用: pm2 stop xui-app-server"
}

# 主函数
main() {
    # 默认值
    ENVIRONMENT=""
    SETUP_ONLY=false
    STOP_ONLY=false
    FORCE_STOP=false
    SKIP_DEPS=false
    SKIP_BUILD=false
    SKIP_DB=false
    SKIP_VALIDATION=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --setup-only)
                SETUP_ONLY=true
                shift
                ;;
            --stop-only)
                STOP_ONLY=true
                shift
                ;;
            --force-stop)
                FORCE_STOP=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --skip-db)
                SKIP_DB=true
                shift
                ;;
            --skip-validation)
                SKIP_VALIDATION=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            dev|staging|production)
                ENVIRONMENT=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果是停止服务
    if [ "$STOP_ONLY" = "true" ] || [ "$FORCE_STOP" = "true" ]; then
        stop_pm2_service
        log_success "服务已停止"
        exit 0
    fi

    # 检查环境参数
    if [ -z "$ENVIRONMENT" ]; then
        log_error "请指定部署环境 (dev|staging|production)"
        show_help
        exit 1
    fi

    log_info "开始 PM2 快速部署..."
    log_info "环境: $ENVIRONMENT"
    
    # 执行部署步骤
    check_requirements
    setup_environment $ENVIRONMENT

    # 安装依赖（在环境验证之前，确保验证脚本能运行）
    if [ "$SKIP_DEPS" != "true" ]; then
        install_dependencies
    else
        log_info "跳过依赖安装"
    fi

    # 验证环境配置（除非跳过）
    if [ "$SKIP_VALIDATION" != "true" ]; then
        validate_environment $ENVIRONMENT
    else
        log_info "跳过环境配置验证"
    fi

    if [ "$SETUP_ONLY" = "true" ]; then
        log_success "环境设置完成，使用 --setup-only 选项，跳过部署"
        exit 0
    fi

    # 构建和数据库设置
    if [ "$SKIP_BUILD" != "true" ]; then
        build_application
    else
        log_info "跳过构建步骤"
    fi

    if [ "$SKIP_DB" != "true" ]; then
        setup_database
    else
        log_info "跳过数据库操作"
    fi
    
    # 执行 PM2 部署
    deploy_pm2
    
    # 健康检查
    sleep 5
    if health_check; then
        show_deployment_info
    else
        log_error "部署可能存在问题，请检查日志"
        exit 1
    fi
}

# 运行主函数
main "$@"
