import 'dotenv/config';
import 'reflect-metadata';
import { serverConfig, databaseConfig } from '@/infrastructure/config';
import { env } from '@/shared/utils/env';

try {
    // 检查环境变量是否成功加载
    if (!env.NODE_ENV) {
        throw new Error('NODE_ENV is not defined. Ensure .env file is loaded.');
    }

    console.log('✓ 环境配置验证通过');
    console.log(`  - 环境: ${serverConfig.env}`);
    console.log(`  - 端口: ${serverConfig.port}`);
    console.log(`  - 数据库: ${databaseConfig.host}:${databaseConfig.port}/${databaseConfig.database}`);

    process.exit(0);
} catch (error) {
    if (error instanceof Error) {
        console.error('❌ 环境配置验证失败:', error.message);
    } else {
        console.error('❌ 环境配置验证失败:', error);
    }
    process.exit(1);
} 