/**
 * Health Check Routes
 * 
 * Routes for health check endpoints using modular architecture.
 */

import { Router, type Router as ExpressRouter } from 'express';
import { container } from 'tsyringe';
import { TYPES } from '@/shared/constants';
import type { HealthController } from '@/modules/health/controllers/health-controller';

const router: ExpressRouter = Router({ strict: true });
const healthController = container.resolve<HealthController>(TYPES.HealthController);

/**
 * @openapi
 * tags:
 *   name: 健康检查
 *   description: 用于监控应用状态的健康检查端点
 */

/**
 * @openapi
 * /api/health:
 *   get:
 *     summary: 基本健康检查
 *     description: 执行基本健康检查并返回应用程序的状态。
 *     tags: [健康检查]
 *     responses:
 *       '200':
 *         description: 应用健康。
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthStatus'
 *       '503':
 *         $ref: '#/components/responses/ServiceUnavailable'
 */
router.get('/', healthController.getBasicHealth);

/**
 * @openapi
 * /api/health/detailed:
 *   get:
 *     summary: 详细健康检查
 *     description: 对应用程序及其依赖项（例如数据库）执行详细的健康检查。
 *     tags: [健康检查]
 *     responses:
 *       '200':
 *         description: 应用和所有依赖项都健康。
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DetailedHealthStatus'
 *       '503':
 *         $ref: '#/components/responses/ServiceUnavailable'
 */
router.get('/detailed', healthController.getDetailedHealth);

/**
 * @openapi
 * /api/health/ready:
 *   get:
 *     summary: 就绪探针
 *     description: 检查应用程序是否准备好接受流量。用于 Kubernetes 就绪探针。
 *     tags: [健康检查]
 *     responses:
 *       '200':
 *         description: 应用已就绪。
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthStatus'
 *       '503':
 *         $ref: '#/components/responses/ServiceUnavailable'
 */
router.get('/ready', healthController.getReadiness);

/**
 * @openapi
 * /api/health/live:
 *   get:
 *     summary: 存活探针
 *     description: 检查应用程序是否正在运行（存活）。用于 Kubernetes 存活探针。
 *     tags: [健康检查]
 *     responses:
 *       '200':
 *         description: 应用正在运行。
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/HealthStatus'
 *       '503':
 *         $ref: '#/components/responses/ServiceUnavailable'
 */
router.get('/live', healthController.getLiveness);

/**
 * @openapi
 * /api/health/ping:
 *   get:
 *     summary: Ping-pong 检查
 *     description: 一个简单的端点，用于检查服务器是否响应。返回 "pong"。
 *     tags: [健康检查]
 *     responses:
 *       '200':
 *         description: 服务器有响应。
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Pong'
 */
router.get('/ping', healthController.getPong);

export default router;
