/**
 * 消息路由
 * 
 * 使用现代模块化架构的消息管理 RESTful API 路由。
 */

import { Router, type IRouter } from 'express';
import { container } from 'tsyringe';
import { TYPES } from '@/shared/constants';
import type { MessageController } from '@/modules/message/controllers';
import {
    validateGetMessage,
    validateDeleteMessage,
    validateSessionMessages,
} from '@/modules/message/validators';
import { validateUserId } from '@/infrastructure/middleware/auth';

const router: IRouter = Router({ strict: true });
const messageController = container.resolve<MessageController>(TYPES.MessageController);

/**
 * @openapi
 * tags:
 *   name: 消息
 *   description: 消息管理端点
 */

/**
 * @openapi
 * /api/message/session/{sessionId}:
 *   get:
 *     summary: 获取会话消息
 *     description: 使用基于游标的分页方式，检索指定会话的消息列表。
 *     tags: [消息]
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要检索消息的会话ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 返回消息的最大数量
 *       - in: query
 *         name: cursor
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 用于分页的游标，即从哪条消息的 `dbId` 开始返回结果
 *     responses:
 *       '200':
 *         description: 成功返回消息列表
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MessageListResponse'
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.get(
    '/session/:sessionId',
    validateUserId,
    ...validateSessionMessages,
    messageController.getSessionMessages
);

/**
 * @openapi
 * /api/message/{dbId}:
 *   get:
 *     summary: 通过数据库ID获取单条消息
 *     description: 通过其唯一的数据库标识符检索单条消息。
 *     tags: [消息]
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: dbId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要检索的消息的唯一数据库ID
 *     responses:
 *       '200':
 *         description: 成功返回消息对象
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Message'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.get(
    '/:dbId',
    validateUserId,
    validateGetMessage,
    messageController.getMessageById
);

/**
 * @openapi
 * /api/message/{dbId}:
 *   delete:
 *     summary: 通过数据库ID删除消息
 *     description: 通过其唯一的数据库标识符删除单条消息。
 *     tags: [消息]
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: dbId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要删除的消息的唯一数据库ID
 *     responses:
 *       '200':
 *         description: 消息删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.delete(
    '/:dbId',
    validateUserId,
    validateDeleteMessage,
    messageController.deleteMessage
);

export default router;
