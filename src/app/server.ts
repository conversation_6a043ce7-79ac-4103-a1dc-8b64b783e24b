/**
 * 应用服务器
 * 
 * HTTP 服务器生命周期管理和优雅关闭。
 */

import http from 'http';
import { createApp, configureRoutes } from './app';
import { initializeContainer, destroyContainer } from './container';
import { serverConfig, gracefulShutdownConfig, databaseConfig } from '@/infrastructure/config';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { databaseConnection } from '@/infrastructure/database/connection';
import { langfuse } from '@/infrastructure/logger/langfuse';

let server: http.Server | null = null;

/**
 * 启动 HTTP 服务器
 */
export async function startServer(): Promise<http.Server> {
    try {
        // 首先初始化服务
        await initializeServices();

        // 同步创建 Express 应用
        const app = createApp();
        
        // 配置路由（在容器初始化后）
        await configureRoutes(app);

        // 创建 HTTP 服务器
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        server = http.createServer(app);

        // 设置优雅关闭
        setupGracefulShutdown();

        // 开始监听
        return new Promise((resolve, reject) => {
            if (!server) {
                reject(new Error('Server not initialized'));
                return;
            }

            server.listen(serverConfig.port, serverConfig.host, (): void => {
                Logger.info(`Server started on ${serverConfig.host}:${serverConfig.port}`, {
                    environment: serverConfig.env,
                    port: serverConfig.port,
                    host: serverConfig.host,
                });
                resolve(server!);
            });

            server.on('error', (error): void => {
                Logger.error('Server startup error', {}, error);
                reject(error);
            });
        });
    } catch (error) {
        Logger.error('Failed to start server', {}, error as Error);
        throw error;
    }
}

/**
 * 初始化应用程序服务
 */
async function initializeServices(): Promise<void> {
    try {
        Logger.info('Initializing services...');
        
        // 初始化TSyringe容器
        await initializeContainer();
        
        // 等待数据库准备就绪（使用配置化的超时时间）
        await waitForDatabaseConnection();

        Logger.info('Services initialized successfully');
    } catch (error) {
        Logger.error('Failed to initialize services', {}, error as Error);
        throw error;
    }
}

/**
 * 等待数据库连接就绪
 */
async function waitForDatabaseConnection(): Promise<void> {
    const maxWaitTime = databaseConfig.connectionTimeoutMillis || 30000; // 默认30秒
    const checkInterval = 1000; // 每秒检查一次
    const startTime = Date.now();
    
    Logger.info(`Waiting for database connection (timeout: ${maxWaitTime}ms)...`);
    
    while (!databaseConnection.isReady()) {
        const elapsedTime = Date.now() - startTime;
        
        if (elapsedTime >= maxWaitTime) {
            const connectionInfo = databaseConnection.getConnectionInfo();
            throw new Error(
                `Database connection timeout after ${maxWaitTime}ms. ` +
                `Connection info: ${JSON.stringify(connectionInfo)}`
            );
        }
        
        // 记录等待进度（每5秒记录一次）
        if (elapsedTime > 0 && elapsedTime % 5000 === 0) {
            Logger.info(`Still waiting for database connection... (${Math.floor(elapsedTime / 1000)}s elapsed)`);
        }
        
        await new Promise<void>(resolve => setTimeout(resolve, checkInterval));
    }
    
    Logger.info('Database connection established successfully');
}

/**
 * 设置优雅关闭处理程序
 */
function setupGracefulShutdown(): void {
    const shutdown = async (signal: string): Promise<void> => {
        Logger.info(`Received ${signal}, starting graceful shutdown...`);

        if (server) {
            server.close((): void => {
                Logger.info('HTTP server closed');

                // 异步处理关闭服务
                shutdownServices()
                    .then((): void => {
                        Logger.info('Graceful shutdown completed');
                        process.exit(0);
                    })
                    .catch((error): void => {
                        Logger.error('Error during shutdown', {}, error as Error);
                        process.exit(1);
                    });
            });

            // 超时后强制关闭
            setTimeout(() => {
                Logger.error('Forced shutdown due to timeout');
                process.exit(1);
            }, gracefulShutdownConfig.timeout);
        } else {
            process.exit(0);
        }
    };

    // 处理终止信号
    process.on('SIGTERM', () => void shutdown('SIGTERM'));
    process.on('SIGINT', () => void shutdown('SIGINT'));

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
        Logger.error('Uncaught exception', {}, error);
        void shutdown('uncaughtException');
    });

    // 处理未处理的 Promise 拒绝
    process.on('unhandledRejection', (reason, promise) => {
        Logger.error('Unhandled promise rejection', {
            reason: String(reason),
            promise: String(promise),
        });
        void shutdown('unhandledRejection');
    });
}

/**
 * 关闭应用程序服务
 */
async function shutdownServices(): Promise<void> {
    try {
        // 刷新 Langfuse 事件
        await langfuse.flush();
        
        // 关闭数据库连接
        await databaseConnection.close();
        
        // 销毁TSyringe容器（包括Langfuse关闭）
        await destroyContainer();

        Logger.info('All services shutdown successfully');
    } catch (error) {
        Logger.error('Error shutting down services', {}, error as Error);
        throw error;
    }
}

/**
 * 获取服务器实例
 */
export function getServer(): http.Server | null {
    return server;
}

export default { startServer, getServer }; 