/**
 * 基础设施配置
 * 
 * 基础设施层的中央配置管理。
 */

import { env, isDevelopment as isDevEnv, isProduction as isProdEnv, isTest as isTestEnv } from '@/shared/utils/env';

// 环境助手
export const isDevelopment = isDevEnv();
export const isProduction = isProdEnv();
export const isTest = isTestEnv();

/**
 * 数据库配置
 */
export const databaseConfig = {
    host: env.DB_HOST,
    port: env.DB_PORT,
    database: env.DB_NAME,
    user: env.DB_USER,
    password: env.DB_PASSWORD,
    ssl: env.DB_SSL,
    max: parseInt(process.env['DB_MAX_CONNECTIONS'] ?? '20', 10),
    idleTimeoutMillis: parseInt(process.env['DB_IDLE_TIMEOUT'] ?? '30000', 10),
    connectionTimeoutMillis: parseInt(process.env['DB_CONNECTION_TIMEOUT'] ?? '10000', 10),
} as const;

/**
 * 服务器配置
 */
export const serverConfig = {
    port: env.PORT,
    host: env.HOST,
    env: env.NODE_ENV,
} as const;

/**
 * CORS配置
 */
export const corsConfig = {
    origin: env.CORS_ORIGIN,
    credentials: env.CORS_CREDENTIALS,
} as const;

/**
 * 日志配置
 */
export const loggingConfig = {
    level: env.LOG_LEVEL,
    format: env.LOG_FORMAT,
} as const;

/**
 * 安全配置
 */
export const securityConfig = {
    bcryptRounds: env.BCRYPT_ROUNDS,
    rateLimitWindowMs: env.RATE_LIMIT_WINDOW_MS,
    rateLimitMaxRequests: env.RATE_LIMIT_MAX_REQUESTS,
} as const;

/**
 * 健康检查配置
 */
export const healthCheckConfig = {
    timeout: env.HEALTH_CHECK_TIMEOUT,
} as const;

/**
 * 优雅关闭配置
 */
export const gracefulShutdownConfig = {
    timeout: env.GRACEFUL_SHUTDOWN_TIMEOUT,
} as const;

/**
 * 指标配置
 */
export const metricsConfig = {
    enabled: env.ENABLE_METRICS,
    port: env.METRICS_PORT,
} as const;

/**
 * Redis配置（可选）
 */
export const redisConfig = (env.REDIS_URL !== undefined && env.REDIS_URL.trim() !== '') ? {
    url: env.REDIS_URL,
    password: env.REDIS_PASSWORD,
} : undefined;

/**
 * Langfuse配置
 */
export const langfuseConfig = {
    publicKey: process.env['LANGFUSE_PUBLIC_KEY'],
    secretKey: process.env['LANGFUSE_SECRET_KEY'],
    baseUrl: process.env['LANGFUSE_BASE_URL'] ?? 'https://cloud.langfuse.com',
    enabled: !!(
        process.env['LANGFUSE_PUBLIC_KEY'] !== undefined 
        && process.env['LANGFUSE_PUBLIC_KEY'].trim() !== ''
        && process.env['LANGFUSE_SECRET_KEY'] !== undefined
        && process.env['LANGFUSE_SECRET_KEY'].trim() !== ''
    ),
} as const;

/**
 * 统一应用配置
 */
export const config = {
    server: serverConfig,
    database: databaseConfig,
    cors: corsConfig,
    logging: loggingConfig,
    security: securityConfig,
    healthCheck: healthCheckConfig,
    gracefulShutdown: gracefulShutdownConfig,
    metrics: metricsConfig,
    redis: redisConfig,
    langfuse: langfuseConfig,
} as const;

export default config; 