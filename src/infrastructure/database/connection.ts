/**
 * 数据库连接服务
 * 
 * 使用 postgres 包管理 PostgreSQL 数据库连接。
 */

import postgres from 'postgres';
import { databaseConfig } from '@/infrastructure/config';
import { logger } from '@/infrastructure/logger';
import type { ServiceHealth } from '@/shared/types';

type PostgresValue = string | number | boolean | null | Date | Buffer | postgres.Parameter;

/**
 * 数据库连接管理器
 */
export class DatabaseConnection {
    private sql: postgres.Sql<{}> | null = null;
    private isConnected = false;
    private connectionRetries = 0;
    private readonly maxRetries = 5;
    private readonly retryDelay = 2000; // 2秒

    constructor() {
        void this.initialize();
    }

    /**
     * 初始化数据库连接
     */
    private async initialize(): Promise<void> {
        try {
            this.sql = postgres({
                host: databaseConfig.host,
                port: databaseConfig.port,
                database: databaseConfig.database,
                username: databaseConfig.user,
                password: databaseConfig.password,
                ssl: databaseConfig.ssl ? { rejectUnauthorized: false } : false,
                max: databaseConfig.max,
                idle_timeout: Math.floor(databaseConfig.idleTimeoutMillis / 1000),
                connect_timeout: Math.floor(databaseConfig.connectionTimeoutMillis / 1000),
                onnotice: () => { }, // 屏蔽通知
                transform: {
                    undefined: null,
                },
            });

            // 测试连接
            await this.testConnection();
            this.isConnected = true;
            this.connectionRetries = 0;
            logger.info('✓ Database connected successfully');
        } catch (error) {
            this.isConnected = false;
            logger.error('❌ Database connection failed:', error);

            if (this.connectionRetries < this.maxRetries) {
                this.connectionRetries++;
                logger.info(
                    `Retrying connection in ${this.retryDelay}ms... (${this.connectionRetries}/${this.maxRetries})`
                );
                setTimeout(() => {
                    void this.initialize();
                }, this.retryDelay);
            } else {
                throw new Error(`Failed to connect to database after ${this.maxRetries} attempts`);
            }
        }
    }

    /**
     * 测试数据库连接
     */
    private async testConnection(): Promise<void> {
        if (!this.sql) {
            throw new Error('Database connection not initialized');
        }

        await this.sql`SELECT 1 as test`;
    }

    /**
     * 获取数据库客户端
     */
    public getClient(): postgres.Sql<{}> {
        if (!this.sql || !this.isConnected) {
            throw new Error('Database not connected');
        }
        return this.sql;
    }

    /**
     * 检查数据库是否已连接
     */
    public isReady(): boolean {
        return this.isConnected && this.sql !== null;
    }

    /**
     * 健康检查
     */
    public async healthCheck(): Promise<ServiceHealth> {
        const startTime = Date.now();

        try {
            if (!this.sql || !this.isConnected) {
                return {
                    status: 'unhealthy',
                    error: 'Database not connected',
                };
            }

            await this.sql`SELECT 1 as health_check`;
            const responseTime = Date.now() - startTime;

            return {
                status: 'healthy',
                responseTime,
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                responseTime: Date.now() - startTime,
                error: error instanceof Error ? error.message : 'Unknown database error',
            };
        }
    }

    /**
     * 执行查询并处理错误
     */
    public async query<T extends Record<string, unknown>>(
        sql: string,
        params: PostgresValue[] = []
    ): Promise<T[]> {
        if (!this.sql || !this.isConnected) {
            throw new Error('Database not connected');
        }

        try {
            // 将参数化查询转换为模板字面量格式
            const result = await this.sql.unsafe(sql, params);
            return result as unknown as T[];
        } catch (error) {
            logger.error('Database query error:', error);
            throw error;
        }
    }

    /**
     * 执行事务
     */
    public async transaction<T>(
        callback: (sql: postgres.Sql<{}>) => Promise<T>
    ): Promise<T> {
        if (!this.sql || !this.isConnected) {
            throw new Error('Database not connected');
        }

        return this.sql.begin(callback) as Promise<T>;
    }

    /**
     * 关闭数据库连接
     */
    public async close(): Promise<void> {
        if (this.sql) {
            await this.sql.end();
            this.sql = null;
            this.isConnected = false;
            logger.info('✓ Database connection closed');
        }
    }

    /**
     * 获取连接信息
     */
    public getConnectionInfo(): {
        isConnected: boolean;
        host: string;
        port: number;
        database: string;
        ssl: boolean;
    } {
        return {
            isConnected: this.isConnected,
            host: databaseConfig.host,
            port: databaseConfig.port,
            database: databaseConfig.database,
            ssl: databaseConfig.ssl,
        };
    }
}

// 导出单例实例
export const databaseConnection = new DatabaseConnection(); 