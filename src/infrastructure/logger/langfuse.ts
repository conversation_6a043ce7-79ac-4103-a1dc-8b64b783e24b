/**
 * Langfuse集成服务
 * 
 * 提供Langfuse客户端初始化和配置。
 */

import { Langfuse } from 'langfuse';
import { langfuseConfig } from '@/infrastructure/config';
import { Logger } from './winston-logger';
import type { 
    JsonValue, 
    LangfuseTrace, 
    LangfuseTraceMetadata,
    LangfuseGenerationConfig,
    LangfuseScoreConfig
} from '@/shared/types';

/**
 * Langfuse服务类
 */
class LangfuseService {
    private client: Langfuse | null = null;
    private readonly enabled: boolean;

    constructor() {
        this.enabled = langfuseConfig.enabled;
        this.initialize();
    }

    /**
     * 初始化Langfuse客户端
     */
    private initialize(): void {
        if (!this.enabled) {
            Logger.info('Langfuse client not configured');
            return;
        }

        try {
            this.client = new Langfuse({
                publicKey: langfuseConfig.publicKey!,
                secretKey: langfuseConfig.secretKey!,
                baseUrl: langfuseConfig.baseUrl,
            });

            Logger.info('Langfuse client initialized successfully');
        } catch (error) {
            Logger.error('Failed to initialize Langfuse client', {}, error as Error);
        }
    }

    /**
     * 检查Langfuse是否启用并配置
     */
    public isEnabled(): boolean {
        return this.enabled && this.client !== null;
    }

    /**
     * 创建新的追踪
     */
    public createTrace(
        name: string,
        input?: JsonValue,
        metadata?: LangfuseTraceMetadata
    ): LangfuseTrace | null {
        if (!this.isEnabled() || !this.client) {
            return null;
        }

        try {
            return this.client.trace({
                name,
                input: input as unknown,
                metadata: metadata as unknown,
            }) as LangfuseTrace;
        } catch (error) {
            Logger.error('Failed to create Langfuse trace', { name }, error as Error);
            return null;
        }
    }

    /**
     * 创建事件
     */
    public createEvent(
        name: string,
        input?: JsonValue,
        output?: JsonValue,
        metadata?: LangfuseTraceMetadata
    ): void {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            this.client.event({
                name,
                input: input as unknown,
                output: output as unknown,
                metadata: metadata as unknown,
            });
        } catch (error) {
            Logger.error('Failed to create Langfuse event', { name }, error as Error);
        }
    }

    /**
     * 创建生成
     */
    public createGeneration(config: LangfuseGenerationConfig): void {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            this.client.generation(config);
        } catch (error) {
            Logger.error('Failed to create Langfuse generation', { name: config.name }, error as Error);
        }
    }

    /**
     * 为追踪评分
     */
    public scoreTrace(config: LangfuseScoreConfig): void {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            this.client.score(config);
        } catch (error) {
            Logger.error('Failed to score Langfuse trace', { traceId: config.traceId }, error as Error);
        }
    }

    /**
     * 刷新待处理事件
     */
    public async flush(): Promise<void> {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            await this.client.flushAsync();
        } catch (error) {
            Logger.error('Failed to flush Langfuse events', {}, error as Error);
        }
    }

    /**
     * 关闭Langfuse客户端
     */
    public async shutdown(): Promise<void> {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            await this.client.shutdownAsync();
            Logger.info('Langfuse client shutdown successfully');
        } catch (error) {
            Logger.error('Failed to shutdown Langfuse client', {}, error as Error);
        }
    }

    /**
     * 获取客户端实例
     */
    public getClient(): Langfuse | null {
        return this.client;
    }
}

// 导出单例实例
export const langfuse = new LangfuseService();
export default langfuse; 