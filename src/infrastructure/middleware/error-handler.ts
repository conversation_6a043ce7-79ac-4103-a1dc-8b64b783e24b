/**
 * Error Handler Middleware
 * 
 * Centralized error handling for Express applications.
 */

import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { createErrorResponse } from '@/shared/utils/response-formatter';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { getErrorStatusCode } from '@/shared/utils/errors';
import { createLogContext } from '@/shared/utils';

/**
 * Extended Request interface with request context
 */
interface ErrorRequest extends Request {
    requestId?: string;
    userId?: string;
}

/**
 * Global error handler middleware
 */
export function errorHandler(
    error: Error,
    req: Request,
    res: Response,
    _next: NextFunction
): void {
    const errorReq = req as ErrorRequest;
    const requestId = errorReq['requestId'];
    const userId = errorReq['userId'];

    // Log the error with safe context
    const logContext = createLogContext({
        error: error.message,
        stack: error.stack,
        requestId,
        userId,
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip ?? 'unknown',
    });

    Logger.error('Unhandled error occurred', logContext);

    // Prepare error response
    const statusCode = getErrorStatusCode(error);
    const message = statusCode === StatusCodes.INTERNAL_SERVER_ERROR ? 'Internal Server Error' : error.message;

    const errorResponse = createErrorResponse(
        message,
        'An error occurred while processing your request'
    );

    res.status(statusCode).json(errorResponse);
}

/**
 * 404 Not Found handler
 */
export function notFoundHandler(
    req: Request,
    res: Response
): void {
    // Log with safe context
    const logContext = createLogContext({
        method: req.method,
        path: req.path,
        query: req.query,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
    });

    Logger.warn('Route not found', logContext);

    const errorResponse = createErrorResponse(
        'Route not found',
        `The requested resource ${req.method} ${req.path} was not found`
    );

    res.status(StatusCodes.NOT_FOUND).json(errorResponse);
}
