import swaggerJsdoc from 'swagger-jsdoc';
import type { Express } from 'express';
import swaggerUi from 'swagger-ui-express';
import { serverConfig } from '@/shared/constants';
import { logInfo } from '@/shared/utils';

const options: swaggerJsdoc.Options = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'XUI App Server API',
            version: '1.0.0',
            description: 'XUI 应用服务 API 文档',
        },
        servers: [
            {
                url: `http://${serverConfig.host}:${serverConfig.port}`,
                description: '开发服务器',
            },
        ],
        components: {
            securitySchemes: {
                userIdHeader: {
                    type: 'apiKey',
                    in: 'header',
                    name: 'userid',
                    description: '用户认证ID',
                },
            },
            schemas: {
                // Agent Schemas
                Agent: {
                    type: 'object',
                    properties: {
                        id: { type: 'string', format: 'uuid' },
                        name: { type: 'string' },
                        avatar: { type: 'string', format: 'uri' },
                        cardUrl: { type: 'string', format: 'uri' },
                        type: { type: 'integer', description: '1: 创作协作类, 2: 沉浸式教学类' },
                        group: { type: 'integer', description: '1: AI 老师, 2: AI 对练, 3: Leader Mate' },
                        target: { type: 'integer', description: '1: 学员, 2: 管理员, 3: 两者' },
                        status: { type: 'integer', description: '0: 已删除, 1: 正常, 2: 下架' },
                        umdUrl: { type: 'string', format: 'uri', nullable: true },
                        userId: { type: 'string', format: 'uuid' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' },
                    },
                },
                CreateAgentRequest: {
                    type: 'object',
                    required: ['name', 'avatar', 'cardUrl', 'type', 'group', 'target', 'status'],
                    properties: {
                        name: { type: 'string', maxLength: 255, minLength: 1 },
                        avatar: { type: 'string', format: 'uri' },
                        cardUrl: { type: 'string', format: 'uri' },
                        type: { type: 'integer', enum: [1, 2] },
                        group: { type: 'integer', enum: [1, 2, 3] },
                        target: { type: 'integer', enum: [1, 2, 3] },
                        status: { type: 'integer', enum: [0, 1, 2] },
                        umdUrl: { type: 'string', format: 'uri', nullable: true },
                    },
                },
                UpdateAgentRequest: {
                    type: 'object',
                    properties: {
                        name: { type: 'string', maxLength: 255, minLength: 1 },
                        avatar: { type: 'string', format: 'uri' },
                        cardUrl: { type: 'string', format: 'uri' },
                        type: { type: 'integer', enum: [1, 2] },
                        group: { type: 'integer', enum: [1, 2, 3] },
                        target: { type: 'integer', enum: [1, 2, 3] },
                        status: { type: 'integer', enum: [0, 1, 2] },
                        umdUrl: { type: 'string', format: 'uri', nullable: true },
                    },
                },
                AgentResponse: {
                     type: 'object',
                     properties: {
                        id: { type: 'string', format: 'uuid' },
                        name: { type: 'string' },
                        avatar: { type: 'string', format: 'uri' },
                        cardUrl: { type: 'string', format: 'uri' },
                        type: { type: 'integer' },
                        typeDescription: { type: 'string' },
                        group: { type: 'integer' },
                        groupDescription: { type: 'string' },
                        target: { type: 'integer' },
                        targetDescription: { type: 'string' },
                        status: { type: 'integer' },
                        statusDescription: { type: 'string' },
                        umdUrl: { type: 'string', format: 'uri', nullable: true },
                        userId: { type: 'string', format: 'uuid' },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' },
                     }
                },
                ListAgentsResponse: {
                    type: 'object',
                    properties: {
                        agents: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/AgentResponse' }
                        },
                        pagination: {
                            type: 'object',
                            properties: {
                                page: { type: 'integer' },
                                limit: { type: 'integer' },
                                total: { type: 'integer' },
                                totalPages: { type: 'integer' },
                                hasNext: { type: 'boolean' },
                                hasPrev: { type: 'boolean' },
                            }
                        }
                    }
                },
                // Health Schemas
                HealthStatus: {
                    type: 'object',
                    properties: {
                        status: { type: 'string', example: 'OK' },
                        timestamp: { type: 'string', format: 'date-time' },
                    },
                },
                DetailedHealthStatus: {
                    type: 'object',
                    properties: {
                        status: { type: 'string', example: 'OK' },
                        timestamp: { type: 'string', format: 'date-time' },
                        checks: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    name: { type: 'string', example: 'database' },
                                    status: { type: 'string', example: 'OK' },
                                    details: { type: 'object', additionalProperties: true },
                                },
                            },
                        },
                    },
                },
                Pong: {
                    type: 'object',
                    properties: {
                        message: { type: 'string', example: 'pong' },
                    },
                },
                // Message Schemas
                MessageContent: {
                    type: 'object',
                    properties: {
                        type: { type: 'string', enum: ['text', 'file', 'data'] },
                        text: { type: 'string' },
                        file: {
                            type: 'object',
                            properties: {
                                bytes: { type: 'string', format: 'byte' },
                                uri: { type: 'string', format: 'uri' },
                                metadata: { type: 'object' }
                            }
                        },
                        data: { type: 'object' },
                        metadata: { type: 'object' }
                    }
                },
                MessageSender: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        name: { type: 'string' },
                        avatar: { type: 'string', format: 'uri' },
                        type: { type: 'string' }
                    }
                },
                Message: {
                    type: 'object',
                    properties: {
                        dbId: { type: 'string', format: 'uuid' },
                        id: { type: 'string' },
                        sessionId: { type: 'string', format: 'uuid' },
                        role: { type: 'string', enum: ['user', 'assistant'] },
                        content: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/MessageContent' }
                        },
                        sender: { $ref: '#/components/schemas/MessageSender', nullable: true },
                        extendedData: { type: 'object', nullable: true },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' },
                        userId: { type: 'string', format: 'uuid', nullable: true }
                    }
                },
                MessageListResponse: {
                    type: 'object',
                    properties: {
                        messages: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/Message' }
                        },
                        hasNextPage: { type: 'boolean' },
                        nextCursor: { type: 'string', format: 'uuid', nullable: true }
                    }
                },
                // Session Schemas
                Session: {
                    type: 'object',
                    properties: {
                        id: { type: 'string', format: 'uuid' },
                        title: { type: 'string' },
                        userId: { type: 'string', format: 'uuid' },
                        agentId: { type: 'string', format: 'uuid' },
                        metadata: { type: 'object', nullable: true },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                },
                SessionWithAgent: {
                    type: 'object',
                    properties: {
                        id: { type: 'string', format: 'uuid' },
                        title: { type: 'string' },
                        userId: { type: 'string', format: 'uuid' },
                        agentId: { type: 'string', format: 'uuid' },
                        agent: { $ref: '#/components/schemas/Agent' },
                        metadata: { type: 'object', nullable: true },
                        createdAt: { type: 'string', format: 'date-time' },
                        updatedAt: { type: 'string', format: 'date-time' }
                    }
                },
                CreateSessionRequest: {
                    type: 'object',
                    required: ['agentId'],
                    properties: {
                        agentId: { type: 'string', format: 'uuid' }
                    }
                },
                UpdateSessionRequest: {
                    type: 'object',
                    required: ['title'],
                    properties: {
                        title: { type: 'string', maxLength: 200, minLength: 1 }
                    }
                },
                ChatRequest: {
                    type: 'object',
                    required: ['agentId', 'message'],
                    properties: {
                        agentId: { type: 'string', format: 'uuid' },
                        message: { $ref: '#/components/schemas/Message' }
                    }
                },
                ListSessionsResponse: {
                    type: 'object',
                    properties: {
                        sessions: {
                            type: 'array',
                            items: { $ref: '#/components/schemas/SessionWithAgent' }
                        },
                        pagination: {
                            type: 'object',
                            properties: {
                                page: { type: 'integer' },
                                limit: { type: 'integer' },
                                total: { type: 'integer' },
                                totalPages: { type: 'integer' },
                                hasNext: { type: 'boolean' },
                                hasPrev: { type: 'boolean' }
                            }
                        }
                    }
                },
                // Common Responses
                ApiSuccess: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean', example: true },
                        data: { type: 'object' },
                        message: { type: 'string', nullable: true },
                        timestamp: { type: 'string', format: 'date-time' },
                    },
                },
                ApiError: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean', example: false },
                        error: { type: 'string' },
                        message: { type: 'string' },
                        timestamp: { type: 'string', format: 'date-time' },
                        requestId: { type: 'string', format: 'uuid', nullable: true },
                    },
                },
            },
            responses: {
                UnauthorizedError: {
                    description: '未授权访问',
                    content: {
                        'application/json': {
                            schema: { $ref: '#/components/schemas/ApiError' },
                            example: {
                                success: false,
                                error: "Unauthorized",
                                message: "Token is missing or invalid",
                                timestamp: "2023-10-27T10:00:00.000Z"
                            }
                        }
                    }
                },
                NotFoundError: {
                    description: '资源未找到',
                    content: {
                        'application/json': {
                            schema: { $ref: '#/components/schemas/ApiError' },
                            example: {
                                success: false,
                                error: "Not Found",
                                message: "Resource not found",
                                timestamp: "2023-10-27T10:00:00.000Z"
                            }
                        }
                    }
                },
                BadRequestError: {
                    description: '错误的请求',
                    content: {
                        'application/json': {
                            schema: { $ref: '#/components/schemas/ApiError' },
                            example: {
                                success: false,
                                error: "Bad Request",
                                message: "Invalid input",
                                timestamp: "2023-10-27T10:00:00.000Z"
                            }
                        }
                    }
                },
                InternalServerError: {
                    description: '服务器内部错误',
                    content: {
                        'application/json': {
                            schema: { $ref: '#/components/schemas/ApiError' },
                            example: {
                                success: false,
                                error: "Internal Server Error",
                                message: "An unexpected error occurred",
                                timestamp: "2023-10-27T10:00:00.000Z"
                            }
                        }
                    }
                },
                ServiceUnavailable: {
                    description: '服务不可用',
                    content: {
                        'application/json': {
                            schema: { $ref: '#/components/schemas/ApiError' },
                            example: {
                                success: false,
                                error: "Service Unavailable",
                                message: "服务不可用",
                                timestamp: "2023-10-27T10:00:00.000Z"
                            }
                        }
                    }
                }
            }
        },
        security: [
            {
                userIdHeader: [],
            },
        ],
    },
    // APIs to document
    apis: ['./src/app/routes/*.ts'],
};

const swaggerSpec = swaggerJsdoc(options);

export const setupSwagger = (app: Express): void => {
    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
    logInfo(`Swagger docs available at http://${serverConfig.host}:${serverConfig.port}/api-docs`);
}; 