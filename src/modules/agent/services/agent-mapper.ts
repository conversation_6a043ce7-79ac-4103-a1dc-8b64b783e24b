import { injectable } from 'tsyringe';
import {
    Agent,
    AgentResponseDto,
    getAgentStatusDescription,
    getAgentTargetDescription,
    getAgentTypeDescription,
    getAgentGroupDescription,
} from '../dto/agent-dto';
import { type AgentWithSessions } from '../repositories/agent-repository';

@injectable()
export class AgentMapper {
    public toDto(agent: Agent): AgentResponseDto {
        return {
            ...agent,
            typeDescription: getAgentTypeDescription(agent.type),
            groupDescription: getAgentGroupDescription(agent.group),
            targetDescription: getAgentTargetDescription(agent.target),
            statusDescription: getAgentStatusDescription(agent.status),
            createdAt: agent.createdAt.toISOString(),
            updatedAt: agent.updatedAt.toISOString(),
            umdUrl: agent.umdUrl,
        };
    }

    public toDtoList(agents: Agent[]): AgentResponseDto[];
    public toDtoList(agents: AgentWithSessions[]): AgentResponseDto[];
    public toDtoList(agents: (Agent | AgentWithSessions)[]): AgentResponseDto[] {
        return agents.map(agent => {
            const dto = this.toDto(agent);
            // If agent has latestSessions, we could add them to the DTO
            // For now, we'll just pass through the basic agent data
            return dto;
        });
    }
} 