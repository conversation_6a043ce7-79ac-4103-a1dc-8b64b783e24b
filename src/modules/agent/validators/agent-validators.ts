/**
 * Agent 验证器
 * 
 * 用于验证 agent 相关请求的 Express 中间件。
 */

import type { Request, Response, NextFunction, RequestHandler } from 'express';
import { createValidator, commonSchemas } from '@/shared/utils/validator-helpers';
import { createErrorResponse } from '@/shared/utils/response-formatter';
import {
    CreateAgentRequestSchema,
    UpdateAgentRequestSchema,
    AgentListQuerySchema,
} from '../dto/agent-dto';
import { z } from 'zod/v4';
import { type AuthenticatedRequest } from '@/shared/types';

/**
 * 创建用于验证URL参数的 Express 中间件
 */
function createParamsValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            (req as AuthenticatedRequest).validatedParams = validator(req.params) as Record<string, unknown>;
            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(400).json(createErrorResponse(
                    validationError.message,
                    validationError.details
                ));
            } else {
                res.status(400).json(createErrorResponse(
                    `无效的 ${description}`,
                    validationError.message
                ));
            }
        }
    };
}

/**
 * 创建用于验证请求体的 Express 中间件
 */
function createBodyValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            (req as AuthenticatedRequest).validatedBody = validator(req.body) as Record<string, unknown>;
            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(400).json(createErrorResponse(
                    validationError.message,
                    validationError.details
                ));
            } else {
                res.status(400).json(createErrorResponse(
                    `无效的 ${description}`,
                    validationError.message
                ));
            }
        }
    };
}

/**
 * 创建用于验证查询参数的 Express 中间件
 */
function createQueryValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            (req as AuthenticatedRequest).validatedQuery = validator(req.query) as Record<string, unknown>;
            
            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(400).json(createErrorResponse(
                    validationError.message,
                    validationError.details
                ));
            } else {
                res.status(400).json(createErrorResponse(
                    `无效的 ${description}`,
                    validationError.message
                ));
            }
        }
    };
}

/**
 * 使用通用工具预构建的验证器
 */
export const validateAgentId: RequestHandler = createParamsValidator(
    z.object({ agentId: commonSchemas.uuid }), 
    '无效的 agent ID'
);
export const validateCreateAgentRequest: RequestHandler = createBodyValidator(
    CreateAgentRequestSchema as unknown as z.ZodType, '创建 agent 请求'
);
export const validateUpdateAgentRequest: RequestHandler = createBodyValidator(
    UpdateAgentRequestSchema as unknown as z.ZodType, '更新 agent 请求'
);
export const validateAgentListQuery: RequestHandler = createQueryValidator(
    AgentListQuerySchema as unknown as z.ZodType, 'agent 列表查询'
);

/**
 * 不同操作的组合验证器数组
 */
export const validateCreateAgent: RequestHandler[] = [
    validateCreateAgentRequest,
];

export const validateUpdateAgent: RequestHandler[] = [
    validateAgentId,
    validateUpdateAgentRequest,
];

export const validateGetAgent: RequestHandler[] = [
    validateAgentId,
];

export const validateDeleteAgent: RequestHandler[] = [
    validateAgentId,
];

export const validateListAgents: RequestHandler[] = [
    validateAgentListQuery,
];