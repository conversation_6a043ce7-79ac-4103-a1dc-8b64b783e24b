/**
 * Message Service
 * 
 * Business logic layer for message operations.
 * Handles message creation, retrieval, and validation.
 */

import { injectable, inject } from 'tsyringe';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { MessageRepository } from '../repositories';
import { MessageMapper } from './message-mapper';
import { NotFoundError } from '@/shared/utils/errors';
import type { 
    CreateMessageRequestDto,
    MessageListQueryDto, 
    MessageListResponseDto, 
    MessageResponseDto,
} from '../dto';
import { TYPES } from '@/shared/constants';
import type { CreateMessageEntity } from '../repositories/message-repository';

@injectable()
export class MessageService {
    constructor(
        @inject(TYPES.MessageRepository) private readonly messageRepository: MessageRepository,
        @inject(TYPES.Logger) private readonly logger: typeof Logger,
        @inject(TYPES.MessageMapper) private readonly messageMapper: MessageMapper
    ) {}

    /**
     * Get message by ID with access control
     */
    async getMessageById(messageId: string, userId: string): Promise<MessageResponseDto> {
        const message = await this.messageRepository.findById(messageId);
        
        if (!message) {
            throw new NotFoundError('Message not found');
        }

        const hasAccess = await this.messageRepository.verifySessionAccess(message.sessionId, userId);
        if (!hasAccess) {
            throw new NotFoundError('Message not found'); // Don't reveal existence
        }

        this.logger.info('Message retrieved successfully', {
            messageId,
            userId,
            sessionId: message.sessionId,
        });

        return this.messageMapper.toDto(message);
    }

    /**
     * Get session messages with pagination and filtering
     */
    async getSessionMessages(
        userId: string, 
        sessionId: string,
        query: MessageListQueryDto
    ): Promise<MessageListResponseDto> {
        const hasAccess = await this.messageRepository.verifySessionAccess(sessionId, userId);
        if (!hasAccess) {
            throw new NotFoundError('Session not found');
        }

        const result = await this.messageRepository.findBySession(sessionId, query);

        this.logger.info('Session messages retrieved successfully', {
            userId,
            sessionId: sessionId,
            cursor: query.cursor,
            nextCursor: result.nextCursor,
            hasNextPage: result.hasNextPage,
            limit: result.limit,
        });

        return {
            messages: result.messages,
            hasNextPage: result.hasNextPage,
            nextCursor: result.nextCursor ?? undefined,
            limit: result.limit,
        };
    }

    /**
     * Delete message
     */
    async deleteMessage(messageId: string, userId: string): Promise<{ id: string }> {
        const message = await this.messageRepository.findById(messageId);
        if (!message) {
            throw new NotFoundError('Message not found');
        }
        
        const hasAccess = await this.messageRepository.verifySessionAccess(message.sessionId, userId);
        if (!hasAccess) {
            throw new NotFoundError('Message not found');
        }
        
        await this.messageRepository.delete(messageId);

        this.logger.info('Message deleted successfully', {
            messageId,
            userId,
            sessionId: message.sessionId,
        });

        return { id: messageId };
    }

    async createMessage(message: CreateMessageRequestDto): Promise<CreateMessageEntity> {
        const createdMessage =  await this.messageRepository.create(message as CreateMessageEntity);
        return createdMessage;
    }

    async hasMessagesBySessionId(sessionId: string): Promise<boolean> {
        return await this.messageRepository.hasMessageBySession(sessionId);
    }
} 