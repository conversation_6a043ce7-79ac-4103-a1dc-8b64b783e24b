/**
 * Message Validators
 * 
 * Express middleware for validating message-related requests.
 * Provides reusable validation functions for message operations.
 */

import type { Request, Response, NextFunction, RequestHandler } from 'express';
import { createValidator, commonSchemas } from '@/shared/utils/validator-helpers';
import { createErrorResponse } from '@/shared/utils/response-formatter';
import {
    CreateMessageRequestDtoSchema,
    MessageListQueryDtoSchema,
} from '../dto/message-dto';
import { z } from 'zod/v4';
import { type AuthenticatedRequest } from '@/shared/types';

/**
 * Creates an Express middleware for validating request parameters.
 */
function createParamsValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            const authReq = req as AuthenticatedRequest;
            authReq.validatedParams = {
                ...(authReq.validatedParams ?? {}),
                ...validator(req.params) as Record<string, unknown>,
            };
            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(400).json(createErrorResponse(
                    validationError.message,
                    validationError.details
                ));
            } else {
                res.status(400).json(createErrorResponse(
                    `无效的 ${description}`,
                    validationError.message
                ));
            }
        }
    };
}

/**
 * Creates an Express middleware for validating the request body.
 */
function createBodyValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            (req as AuthenticatedRequest).validatedBody = validator(req.body) as Record<string, unknown>;
            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(400).json(createErrorResponse(
                    validationError.message,
                    validationError.details
                ));
            } else {
                res.status(400).json(createErrorResponse(
                    `无效的 ${description}`,
                    validationError.message
                ));
            }
        }
    };
}

/**
 * Creates an Express middleware for validating query parameters.
 */
function createQueryValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            (req as AuthenticatedRequest).validatedQuery = validator(req.query) as Record<string, unknown>;
            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(400).json(createErrorResponse(
                    validationError.message,
                    validationError.details
                ));
            } else {
                res.status(400).json(createErrorResponse(
                    `无效的 ${description}`,
                    validationError.message
                ));
            }
        }
    };
}

/**
 * Pre-built validators using the generic factory functions
 */
export const validateMessageId: RequestHandler = createParamsValidator(
    z.object({ messageId: commonSchemas.uuid }),
    'Invalid message ID'
);
export const validateSessionIdParam: RequestHandler = createParamsValidator(
    z.object({ sessionId: commonSchemas.uuid }),
    'Invalid session ID'
);

export const validateCreateMessageRequest: RequestHandler = createBodyValidator(
    CreateMessageRequestDtoSchema as unknown as z.ZodType, 'create message request'
);
export const validateMessageListQuery: RequestHandler = createQueryValidator(
    MessageListQueryDtoSchema as unknown as z.ZodType, 'message list query'
);

/**
 * Combined validator arrays for different operations
 */
export const validateCreateMessage: RequestHandler[] = [
    validateCreateMessageRequest,
];

export const validateListMessages: RequestHandler[] = [
    validateMessageListQuery,
];

export const validateGetMessage: RequestHandler[] = [
    validateMessageId,
];

export const validateDeleteMessage: RequestHandler[] = [
    validateMessageId,
];

/**
 * Combined validator for session messages
 */
export const validateSessionMessages: RequestHandler[] = [
    validateSessionIdParam,
    validateMessageListQuery,
];


/**
 * Combined validator for message operations
 */
export const validateMessageOperation: RequestHandler[] = [
    validateMessageId,
];