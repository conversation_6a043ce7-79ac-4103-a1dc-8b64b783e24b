/**
 * Session Service
 * 
 * Business logic layer for session operations.
 * Handles session creation, retrieval, update, and deletion with proper validation.
 */

import { injectable, inject } from 'tsyringe';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { SessionRepository, type SessionEntity } from '../repositories';
import { SessionMapper } from './session-mapper';
import { NotFoundError, ValidationError } from '@/shared/utils/errors';
import { TYPES } from '@/shared/constants';
import type { 
    SessionListQueryDto,
    CreateSessionRequestDto,
    UpdateSessionRequestDto,
    SessionResponseDto,
    SessionWithAgentResponseDto,
    SessionAgentInfo,
} from '../dto';
import { AgentRepository } from '@/modules/agent/repositories';
import { 
    getAgentTypeDescription, 
    getAgentTargetDescription, 
    getAgentStatusDescription, 
    getAgentGroupDescription
} from '@/modules/agent/dto/agent-dto';

export interface PaginatedSessionResponse {
    sessions: SessionWithAgentResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}

@injectable()
export class SessionService {
    constructor(
        @inject(TYPES.SessionRepository) private readonly sessionRepository: SessionRepository,
        @inject(TYPES.AgentRepository) private readonly agentRepository: AgentRepository,
        @inject(TYPES.Logger) private readonly logger: typeof Logger,
        @inject(TYPES.SessionMapper) private readonly sessionMapper: SessionMapper
    ) {}

    /**
     * Get user sessions with pagination and filtering
     */
    async getUserSessions(userId: string, query: SessionListQueryDto): Promise<PaginatedSessionResponse> {
        const result = await this.sessionRepository.findByUser(userId, query);
        
        // 获取所有唯一的agentId
        const agentIds = [...new Set(result.sessions.map(session => session.agentId))];
        
        // 批量获取agent信息
        const agents = await Promise.all(
            agentIds.map(agentId => this.agentRepository.findById(agentId))
        );
        
        // 创建agentId到agent的映射
        const agentMap = new Map(
            agents
                .filter(agent => agent !== null)
                .map(agent => [agent!.id, agent!])
        );
        
        // 为每个session创建SessionAgentInfo并组合数据
        const sessionsWithAgents = result.sessions
            .map(session => {
                const agent = agentMap.get(session.agentId);
                if (!agent) {
                    // 如果agent不存在，记录警告但继续处理
                    this.logger.warn('Agent not found for session', { 
                        sessionId: session.id, 
                        agentId: session.agentId 
                    });
                    return null;
                }
                
                const agentInfo: SessionAgentInfo = {
                    id: agent.id,
                    name: agent.name,
                    avatar: agent.avatar,
                    type: agent.type,
                    typeDescription: getAgentTypeDescription(agent.type),
                    target: agent.target,
                    targetDescription: getAgentTargetDescription(agent.target),
                    status: agent.status,
                    statusDescription: getAgentStatusDescription(agent.status),
                    group: agent.group,
                    groupDescription: getAgentGroupDescription(agent.group),
                };
                
                return { session, agent: agentInfo };
            })
            .filter(item => item !== null) as Array<{ session: SessionEntity; agent: SessionAgentInfo }>;
        
        this.logger.info('User sessions retrieved successfully', {
            userId,
            total: result.total,
            page: result.page,
            limit: result.limit,
            count: sessionsWithAgents.length,
        });

        return {
            ...result,
            sessions: this.sessionMapper.toDtoListWithAgent(sessionsWithAgents),
        };
    }

    /**
     * Get session by ID with access control
     */
    async getSessionById(sessionId: string, userId: string): Promise<SessionResponseDto> {
        const session = await this.sessionRepository.findById(sessionId);

        if (!session || session.userId !== userId) {
            throw new NotFoundError('Session not found');
        }

        this.logger.info('Session retrieved successfully', {
            sessionId,
            userId,
            sessionTitle: session.title,
            agentId: session.agentId,
        });

        return this.sessionMapper.toDto(session);
    }

    /**
     * Create new session
     */
    async createSession(userId: string, query: CreateSessionRequestDto): Promise<SessionResponseDto> {
        const {agentId} = query;
        const agent = await this.agentRepository.findById(agentId);
        if (!agent) {
            throw new NotFoundError('Agent not found');
        }

        const title = `与 ${agent.name} 的对话`;
        const createdSession = await this.sessionRepository.create({
            agentId,
            userId,
            title
        });

        this.logger.info('Session created successfully', {
            sessionId: createdSession.id,
            userId,
            title,
            agentId,
        });

        return this.sessionMapper.toDto(createdSession);
    }

    /**
     * Update session with access control
     */
    async updateSession(sessionId: string, userId: string, data: UpdateSessionRequestDto): Promise<SessionResponseDto> {
        const existingSession = await this.sessionRepository.findById(sessionId);
        if (!existingSession || existingSession.userId !== userId) {
            throw new NotFoundError('Session not found');
        }

        const updatedSession = await this.sessionRepository.update(sessionId, data);

        if (!updatedSession) {
            throw new NotFoundError('Session not found after update');
        }

        this.logger.info('Session updated successfully', {
            sessionId,
            userId,
            updatedFields: Object.keys(data),
            sessionTitle: updatedSession.title,
        });

        return this.sessionMapper.toDto(updatedSession);
    }

    /**
     * Delete session with access control
     */
    async deleteSession(sessionId: string, userId: string): Promise<{ id: string }> {
        if (!sessionId || !userId) {
            throw new ValidationError('Session ID and User ID are required');
        }

        const session = await this.sessionRepository.findById(sessionId);
        if (!session || session.userId !== userId) {
            throw new NotFoundError('Session not found');
        }
        
        await this.sessionRepository.deleteSessionMessages(sessionId);
        const deleted = await this.sessionRepository.delete(sessionId);
        
        if (!deleted) {
            throw new NotFoundError('Session not found');
        }

        this.logger.info('Session deleted successfully', {
            sessionId,
            userId,
            sessionTitle: session.title,
            agentId: session.agentId,
        });

        return { id: sessionId };
    }
} 