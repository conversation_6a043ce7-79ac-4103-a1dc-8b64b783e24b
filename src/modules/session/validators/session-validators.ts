/**
 * Session Validators
 * 
 * Express middleware for validating session-related requests.
 * Provides reusable validation functions for session operations.
 */

import type { Request, Response, NextFunction, RequestHandler } from 'express';
import { createValidator, commonSchemas } from '@/shared/utils/validator-helpers';
import { createErrorResponse } from '@/shared/utils/response-formatter';
import {
    CreateSessionRequestDtoSchema,
    UpdateSessionRequestDtoSchema,
    SessionListQueryDtoSchema,
    ChatRequestDtoSchema,
} from '../dto/session-dto';
import { z } from 'zod/v4';
import { type AuthenticatedRequest } from '@/shared/types';

/**
 * Creates an Express middleware for validating request parameters.
 */
function createParamsValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            const authReq = req as AuthenticatedRequest;
            authReq.validatedParams = {
                ...(authReq.validatedParams ?? {}),
                ...validator(req.params) as Record<string, unknown>,
            };
            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(400).json(createErrorResponse(
                    validationError.message,
                    validationError.details
                ));
            } else {
                res.status(400).json(createErrorResponse(
                    `无效的 ${description}`,
                    validationError.message
                ));
            }
        }
    };
}

/**
 * Creates an Express middleware for validating the request body.
 */
function createBodyValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            (req as AuthenticatedRequest).validatedBody = validator(req.body) as Record<string, unknown>;
            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(400).json(createErrorResponse(
                    validationError.message,
                    validationError.details
                ));
            } else {
                res.status(400).json(createErrorResponse(
                    `无效的 ${description}`,
                    validationError.message
                ));
            }
        }
    };
}

/**
 * Creates an Express middleware for validating query parameters.
 */
function createQueryValidator<T>(schema: z.ZodSchema<T>, description: string): RequestHandler {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const validator = createValidator(schema, `无效的 ${description}`);
            (req as AuthenticatedRequest).validatedQuery = validator(req.query) as Record<string, unknown>;
            next();
        } catch (error) {
            const validationError = error as Error & { code?: string; details?: string | object };
            if (validationError.code === 'VALIDATION_ERROR') {
                res.status(400).json(createErrorResponse(
                    validationError.message,
                    validationError.details
                ));
            } else {
                res.status(400).json(createErrorResponse(
                    `无效的 ${description}`,
                    validationError.message
                ));
            }
        }
    };
}

/**
 * Pre-built validators using the generic factory functions
 */
export const validateSessionId: RequestHandler = createParamsValidator(
    z.object({ sessionId: commonSchemas.uuid }),
    'Invalid session ID'
);
export const validateAgentId: RequestHandler = createParamsValidator(
    z.object({ agentId: commonSchemas.uuid }),
    'Invalid agent ID'
);

export const validateCreateSessionRequest: RequestHandler = createQueryValidator(
    CreateSessionRequestDtoSchema as unknown as z.ZodType, 'create session request'
);
export const validateUpdateSessionRequest: RequestHandler = createBodyValidator(
    UpdateSessionRequestDtoSchema as unknown as z.ZodType, 'update session request'
);
export const validateSessionListQuery: RequestHandler = createQueryValidator(
    SessionListQueryDtoSchema as unknown as z.ZodType, 'session list query'
);

export const validateChatRequest: RequestHandler = createBodyValidator(
    ChatRequestDtoSchema as unknown as z.ZodType, 'chat request'
);

/**
 * Combined validator arrays for different operations
 */
export const validateCreateSession: RequestHandler[] = [
    validateCreateSessionRequest,
];

export const validateGetUserSessions: RequestHandler[] = [
    validateSessionListQuery,
];

export const validateGetSession: RequestHandler[] = [
    validateSessionId,
];

export const validateUpdateSession: RequestHandler[] = [
    validateSessionId,
    validateUpdateSessionRequest,
];

export const validateDeleteSession: RequestHandler[] = [
    validateSessionId,
];

export const validateChat: RequestHandler[] = [
    validateSessionId,
    validateChatRequest,
];