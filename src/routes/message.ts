import { Router, type IRouter } from 'express';
import { MessageController } from '@/controllers/message';
import { validateUserId } from '@/middleware/auth';
import { validateQuery, validateParams } from '@/middleware/validation';
import {
    getMessagesQuerySchema,
    messageIdParamSchema,
    getMessageStatsQuerySchema,
    sessionIdParamSchema,
} from '@/validators/message';

const router: IRouter = Router();

/**
 * GET /api/message/session/:sessionId
 * 根据sessionId获取消息列表（游标分页）
 * 首次加载获取最新消息，支持向前翻页获取更早消息，按时间正序排列
 */
router.get(
    '/session/:sessionId',
    validateUserId,
    validateParams(sessionIdParamSchema),
    validateQuery(getMessagesQuerySchema),
    MessageController.getMessagesBySessionId,
);

/**
 * GET /api/message/stats
 * 获取用户消息统计信息
 * 支持时间范围筛选和详细统计
 */
router.get(
    '/stats',
    validateUserId,
    validateQuery(getMessageStatsQuerySchema),
    MessageController.getMessageStats,
);

/**
 * GET /api/message/:id
 * 根据消息ID获取消息内容
 * 验证用户权限，确保只能访问自己的消息
 */
router.get(
    '/:id',
    validateUserId,
    validateParams(messageIdParamSchema),
    MessageController.getMessageById,
);

export default router;
