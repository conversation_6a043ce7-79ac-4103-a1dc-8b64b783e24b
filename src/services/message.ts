import { eq, and, sql, count } from 'drizzle-orm';
import { db } from '@/db';
import { message, type Message, type MessageInsert } from '@/db/schema/message';
import { session } from '@/db/schema/session';
import { logInfo, logError, logDatabaseOperation } from '@/config/logger';
import { measureTime, LangfuseTracer } from '@/utils';
import { NotFoundError } from '@/utils/errors';
import type { CreateMessage } from '@/validators/message';

export interface MessageListResult {
    data: Message[];
    pagination: {
        limit: number;
        hasNextPage: boolean;
        nextCursor: string | null;
    };
}

export interface MessageStatsResult {
    totalMessages: number;
    userMessages: number;
    assistantMessages: number;
    timeRange: string;
    details?: {
        dailyStats: Array<{
            date: string;
            messageCount: number;
        }>;
    };
}

export class MessageService {
    /**
     * 根据sessionId获取消息列表（滚动分页）
     */
    public static async getMessagesBySessionId(
        sessionId: string,
        userId: string,
        limit: number = 20,
        cursor?: string,
    ): Promise<MessageListResult> {
        const startTime = Date.now();
        const trace = LangfuseTracer.createOperationTrace(
            'message.get_by_session',
            { sessionId, userId, limit, cursor },
            {
                service: 'message-service',
                operation: 'get_messages_by_session',
                userId,
                sessionId
            }
        );

        try {
            // 验证会话是否存在且属于当前用户
            const { result: sessionExists, duration: sessionCheckDuration } = await measureTime(
                async (): Promise<Array<{ id: string }>> => {
                    return await db
                        .select({ id: session.id })
                        .from(session)
                        .where(and(eq(session.id, sessionId), eq(session.userId, userId)))
                        .limit(1);
                },
            );

            logDatabaseOperation('SELECT', 'session', sessionCheckDuration, {
                sessionId,
                userId,
                found: sessionExists.length > 0,
            });

            if (sessionExists.length === 0) {
                throw new NotFoundError('Session not found or access denied');
            }

            // 构建查询条件
            const conditions = [eq(message.sessionId, sessionId)];

            // 如果有cursor，需要先获取cursor对应消息的创建时间，然后进行复合查询
            if (cursor !== undefined && cursor.trim() !== '') {
                // 先查询cursor对应的消息信息
                const cursorMessage = await db
                    .select({ createdAt: message.createdAt })
                    .from(message)
                    .where(and(eq(message.dbId, cursor), eq(message.sessionId, sessionId)))
                    .limit(1);

                if (cursorMessage.length > 0) {
                    const cursorCreatedAt = cursorMessage[0]!.createdAt;
                    // 使用复合条件：时间大于cursor时间，或者时间等于cursor时间但ID大于cursor ID
                    // 这样可以获取比cursor更新的消息（用于向后翻页，按时间正序）
                    conditions.push(
                        sql`(${message.createdAt} > ${cursorCreatedAt} OR
                             (${message.createdAt} = ${cursorCreatedAt} AND ${message.dbId} > ${cursor}))`
                    );

                    logInfo('Applied cursor condition for pagination', {
                        cursor,
                        cursorCreatedAt,
                        sessionId,
                        userId
                    });
                } else {
                    // 如果cursor对应的消息不存在，忽略cursor参数
                    logInfo('Cursor message not found, ignoring cursor parameter', {
                        cursor,
                        sessionId,
                        userId
                    });
                }
            }

            // 查询消息列表，按时间正序排列（从旧到新）
            const { result: messages, duration: queryDuration } = await measureTime(async () => {
                return await db
                    .select()
                    .from(message)
                    .where(and(...conditions))
                    .orderBy(message.createdAt) // 改为正序排列
                    .limit(limit + 1); // 多查询一条用于判断是否有下一页
            });

            logDatabaseOperation('SELECT', 'message', queryDuration, {
                sessionId,
                userId,
                limit,
                cursor,
                resultCount: messages.length,
                conditionsCount: conditions.length,
            });

            // 检查是否有下一页
            const hasNextPage = messages.length > limit;
            const resultMessages = hasNextPage ? messages.slice(0, limit) : messages;

            // 消息已经按时间正序排列，无需反转
            const orderedMessages = resultMessages;

            // 获取下一页的cursor（使用最后一条消息的dbId）
            const nextCursor =
                hasNextPage && resultMessages.length > 0 ? resultMessages[resultMessages.length - 1]!.dbId : null;

            logInfo('Successfully retrieved messages by session ID', {
                sessionId,
                userId,
                count: orderedMessages.length,
                hasNextPage,
                cursor,
            });

            // 更新 Langfuse 追踪
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {
                messageCount: orderedMessages.length,
                hasNextPage,
                nextCursor
            }, {
                success: true,
                duration,
                sessionCheckDuration,
                queryDuration
            });

            return {
                data: orderedMessages,
                pagination: {
                    limit,
                    hasNextPage,
                    nextCursor,
                },
            };
        } catch (error) {
            logError(
                'Failed to get messages by session ID',
                {
                    sessionId,
                    userId,
                    limit,
                    cursor,
                },
                error as Error,
            );

            // 更新 Langfuse 追踪错误信息
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, { error: (error as Error).message }, {
                success: false,
                duration,
                error: true,
                errorMessage: (error as Error).message
            });

            LangfuseTracer.traceError({
                error: error as Error,
                operation: 'message.get_by_session',
                context: {
                    service: 'message-service',
                    userId,
                    sessionId,
                    metadata: { limit, cursor }
                }
            });

            throw error;
        }
    }

    /**
     * 获取用户消息统计信息
     */
    public static async getMessageStats(
        userId: string,
        timeRange: string = '30d',
        includeDetails: boolean = false,
    ): Promise<MessageStatsResult> {
        try {
            // 计算时间范围
            let dateFilter = sql`true`;
            if (timeRange !== 'all') {
                const days = parseInt(timeRange.replace('d', ''), 10);
                const startDate = new Date();
                startDate.setDate(startDate.getDate() - days);
                dateFilter = sql`${message.createdAt} >= ${startDate}`;
            }

            // 基础统计查询
            const { result: statsResult, duration: statsDuration } = await measureTime(async () => {
                const result = await db
                    .select({
                        totalMessages: count(message.id),
                        userMessages: count(sql`CASE WHEN ${message.role} = 'user' THEN 1 END`),
                        assistantMessages: count(
                            sql`CASE WHEN ${message.role} = 'assistant' THEN 1 END`,
                        ),
                    })
                    .from(message)
                    .innerJoin(session, eq(message.sessionId, session.id))
                    .where(and(eq(session.userId, userId), dateFilter));

                return result as Array<{
                    totalMessages: number;
                    userMessages: number;
                    assistantMessages: number;
                }>;
            });

            logDatabaseOperation('SELECT', 'message', statsDuration, {
                userId,
                timeRange,
                operation: 'stats',
            });

            const totalStats = statsResult[0] ?? {
                totalMessages: 0,
                userMessages: 0,
                assistantMessages: 0,
            };

            const stats: MessageStatsResult = {
                totalMessages: totalStats.totalMessages,
                userMessages: totalStats.userMessages,
                assistantMessages: totalStats.assistantMessages,
                timeRange,
            };

            // 如果需要详细统计
            if (includeDetails) {
                const { result: dailyStats, duration: detailsDuration } = await measureTime(
                    async () => {
                        return await db
                            .select({
                                date: sql<string>`DATE(${message.createdAt})`,
                                messageCount: count(message.id),
                            })
                            .from(message)
                            .innerJoin(session, eq(message.sessionId, session.id))
                            .where(and(eq(session.userId, userId), dateFilter))
                            .groupBy(sql`DATE(${message.createdAt})`)
                            .orderBy(sql`DATE(${message.createdAt}) DESC`)
                            .limit(30);
                    },
                );

                logDatabaseOperation('SELECT', 'message', detailsDuration, {
                    userId,
                    timeRange,
                    operation: 'daily_stats',
                    resultCount: dailyStats.length,
                });

                stats.details = {
                    dailyStats,
                };
            }

            logInfo('Successfully retrieved message statistics', {
                userId,
                timeRange,
                includeDetails,
                stats: {
                    totalMessages: stats.totalMessages,
                    userMessages: stats.userMessages,
                    assistantMessages: stats.assistantMessages,
                },
            });

            return stats;
        } catch (error) {
            logError(
                'Failed to get message statistics',
                {
                    userId,
                    timeRange,
                    includeDetails,
                },
                error as Error,
            );
            throw error;
        }
    }

    /**
     * 检查session是否有消息记录
     */
    public static async hasMessagesInSession(sessionId: string, userId: string): Promise<boolean> {
        const startTime = Date.now();
        const trace = LangfuseTracer.createOperationTrace(
            'message.has_messages_in_session',
            { sessionId, userId },
            {
                service: 'message-service',
                operation: 'has_messages_in_session',
                userId,
                sessionId
            }
        );

        try {
            // 验证会话是否存在且属于当前用户
            const { result: sessionExists, duration: sessionCheckDuration } = await measureTime(
                async (): Promise<Array<{ id: string }>> => {
                    return await db
                        .select({ id: session.id })
                        .from(session)
                        .where(and(eq(session.id, sessionId), eq(session.userId, userId)))
                        .limit(1);
                },
            );

            logDatabaseOperation('SELECT', 'session', sessionCheckDuration, {
                sessionId,
                userId,
                found: sessionExists.length > 0,
            });

            if (sessionExists.length === 0) {
                throw new NotFoundError('Session not found or access denied');
            }

            // 检查是否有消息记录
            const { result: messageCount, duration: queryDuration } = await measureTime(async () => {
                const result = await db
                    .select({ count: count() })
                    .from(message)
                    .where(eq(message.sessionId, sessionId))
                    .limit(1);
                return result[0]?.count ?? 0;
            });

            logDatabaseOperation('SELECT', 'message', queryDuration, {
                sessionId,
                userId,
                messageCount,
            });

            const hasMessages = messageCount > 0;

            logInfo('Successfully checked messages in session', {
                sessionId,
                userId,
                hasMessages,
                messageCount,
            });

            // 更新 Langfuse 追踪
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {
                hasMessages,
                messageCount
            }, {
                success: true,
                duration,
                sessionCheckDuration,
                queryDuration
            });

            return hasMessages;
        } catch (error) {
            logError(
                'Failed to check messages in session',
                {
                    sessionId,
                    userId,
                },
                error as Error,
            );

            // 更新 Langfuse 追踪
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {}, {
                success: false,
                duration,
                error: error instanceof Error ? error.message : 'Unknown error'
            });

            throw error;
        }
    }

    /**
     * 根据消息ID获取消息内容
     */
    public static async getMessageById(messageId: string, userId: string): Promise<Message | null> {
        try {
            // 查询消息，同时验证用户权限
            const { result: messageResult, duration } = await measureTime(async () => {
                return await db
                    .select()
                    .from(message)
                    .innerJoin(session, eq(message.sessionId, session.id))
                    .where(and(eq(message.dbId, messageId), eq(session.userId, userId)))
                    .limit(1);
            });

            logDatabaseOperation('SELECT', 'message', duration, {
                messageId,
                userId,
                found: messageResult.length > 0,
            });

            const foundMessage = messageResult[0]?.message ?? null;

            if (foundMessage) {
                logInfo('Successfully retrieved message by ID', {
                    messageId,
                    userId,
                    messageRole: foundMessage.role,
                    sessionId: foundMessage.sessionId,
                });
            } else {
                logInfo('Message not found or access denied', {
                    messageId,
                    userId,
                });
            }

            return foundMessage;
        } catch (error) {
            logError(
                'Failed to get message by ID',
                {
                    messageId,
                    userId,
                },
                error as Error,
            );
            throw error;
        }
    }

    /**
     * 创建新消息
     */
    public static async createMessage(
        messageData: CreateMessage,
        userId: string,
    ): Promise<Message> {
        const startTime = Date.now();
        const trace = LangfuseTracer.createOperationTrace(
            'message.create',
            { messageData, userId },
            {
                service: 'message-service',
                operation: 'create_message',
                userId,
                sessionId: messageData.sessionId
            }
        );

        try {
            // 验证会话是否存在且属于当前用户
            const { result: sessionExists, duration: sessionCheckDuration } = await measureTime(
                async () => {
                    return await db
                        .select({ id: session.id, userId: session.userId })
                        .from(session)
                        .where(eq(session.id, messageData.sessionId))
                        .limit(1);
                },
            );

            logDatabaseOperation('SELECT', 'session', sessionCheckDuration, {
                sessionId: messageData.sessionId,
                userId,
                found: sessionExists.length > 0,
            });

            if (sessionExists.length === 0) {
                throw new NotFoundError('Session not found');
            }

            const sessionData = sessionExists[0];
            if (!sessionData || sessionData.userId !== userId) {
                throw new NotFoundError('Access denied: session does not belong to user');
            }

            // 准备插入数据
            const insertData: MessageInsert = {
                id: messageData.id,
                role: messageData.role,
                content: messageData.content as unknown as MessageInsert['content'],
                sender: (messageData.sender as unknown as MessageInsert['sender']) ?? null,
                sessionId: messageData.sessionId,
                userId: messageData.userId ?? userId,
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            // 插入消息
            const { result: createdMessages, duration } = await measureTime(async () => {
                return await db.insert(message).values(insertData).returning();
            });

            const createdMessage = createdMessages[0];

            if (!createdMessage) {
                throw new Error('Failed to create message - no data returned');
            }

            logDatabaseOperation('INSERT', 'message', duration, {
                messageId: createdMessage.id,
                sessionId: messageData.sessionId,
                userId,
                role: messageData.role,
                contentLength: messageData.content.length,
            });

            logInfo('Successfully created new message', {
                messageId: createdMessage.id,
                sessionId: messageData.sessionId,
                userId,
                role: messageData.role,
                contentTypes: messageData.content.map(c => c.type),
            });

            // 更新 Langfuse 追踪
            const totalDuration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {
                messageId: createdMessage.id,
                role: messageData.role,
                contentLength: messageData.content.length,
                contentTypes: messageData.content.map(c => c.type)
            }, {
                success: true,
                duration: totalDuration,
                sessionCheckDuration,
                insertDuration: duration
            });

            return createdMessage;
        } catch (error) {
            logError(
                'Failed to create message',
                {
                    messageData: {
                        id: messageData.id,
                        role: messageData.role,
                        sessionId: messageData.sessionId,
                        contentLength: messageData.content.length,
                    },
                    userId,
                },
                error as Error,
            );

            // 更新 Langfuse 追踪错误信息
            const totalDuration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, { error: (error as Error).message }, {
                success: false,
                duration: totalDuration,
                error: true,
                errorMessage: (error as Error).message
            });

            LangfuseTracer.traceError({
                error: error as Error,
                operation: 'message.create',
                context: {
                    service: 'message-service',
                    userId,
                    sessionId: messageData.sessionId,
                    metadata: {
                        messageId: messageData.id,
                        role: messageData.role,
                        contentLength: messageData.content.length
                    }
                }
            });

            throw error;
        }
    }

    /**
     * 批量创建消息
     */
    public static async createMessages(
        messagesData: CreateMessage[],
        userId: string,
    ): Promise<Message[]> {
        try {
            if (messagesData.length === 0) {
                return [];
            }

            // 获取所有唯一的会话ID
            const sessionIds = [...new Set(messagesData.map(msg => msg.sessionId))];

            // 验证所有会话是否存在且属于当前用户
            const { result: sessions, duration: sessionCheckDuration } = await measureTime(
                async () => {
                    return await db
                        .select({ id: session.id, userId: session.userId })
                        .from(session)
                        .where(
                            and(
                                sql`${session.id} = ANY(${sessionIds})`,
                                eq(session.userId, userId),
                            ),
                        );
                },
            );

            logDatabaseOperation('SELECT', 'session', sessionCheckDuration, {
                sessionIds,
                userId,
                foundCount: sessions.length,
            });

            if (sessions.length !== sessionIds.length) {
                const foundSessionIds = sessions.map(s => s.id);
                const missingSessionIds = sessionIds.filter(id => !foundSessionIds.includes(id));
                throw new NotFoundError(
                    `Sessions not found or access denied: ${missingSessionIds.join(', ')}`,
                );
            }

            // 准备批量插入数据
            const insertData: MessageInsert[] = messagesData.map(messageData => ({
                id: messageData.id,
                role: messageData.role,
                content: messageData.content as unknown as MessageInsert['content'],
                sender: (messageData.sender as unknown as MessageInsert['sender']) ?? null,
                sessionId: messageData.sessionId,
                userId: messageData.userId ?? userId,
                createdAt: new Date(),
                updatedAt: new Date(),
            }));

            // 批量插入消息
            const { result: createdMessages, duration } = await measureTime(async () => {
                return await db.insert(message).values(insertData).returning();
            });

            logDatabaseOperation('INSERT', 'message', duration, {
                messageCount: createdMessages.length,
                sessionIds,
                userId,
                roles: messagesData.map(m => m.role),
            });

            logInfo('Successfully created messages in batch', {
                messageCount: createdMessages.length,
                sessionIds,
                userId,
                messageIds: createdMessages.map(m => m.id),
            });

            return createdMessages;
        } catch (error) {
            logError(
                'Failed to create messages in batch',
                {
                    messageCount: messagesData.length,
                    sessionIds: [...new Set(messagesData.map(msg => msg.sessionId))],
                    userId,
                },
                error as Error,
            );
            throw error;
        }
    }
}
