/**
 * 配置常量
 * 
 * 包含应用程序配置常量和默认值。
 */

import { env } from '@/shared/utils/env';

export const serverConfig = {
    port: env.PORT,
    host: env.HOST,
    env: env.NODE_ENV,
} as const;

export const corsConfig = {
    origin: env.CORS_ORIGIN,
    credentials: env.CORS_CREDENTIALS,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
        'Origin',
        'X-Requested-With', 
        'Content-Type',
        'Accept',
        'Authorization',
        'Cache-Control',
        'X-Request-ID',
        'userid' // 允许前端发送userid请求头
    ],
    exposedHeaders: ['X-Request-ID'],
    maxAge: 86400, // 24小时
} as const;

export const securityConfig = {
    bcryptRounds: env.BCRYPT_ROUNDS,
    rateLimitWindowMs: env.RATE_LIMIT_WINDOW_MS,
    rateLimitMaxRequests: env.RATE_LIMIT_MAX_REQUESTS,
} as const;

export const databaseConfig = {
    host: env.DB_HOST,
    port: env.DB_PORT,
    database: env.DB_NAME,
    user: env.DB_USER,
    password: env.DB_PASSWORD,
    ssl: env.DB_SSL,
} as const;

export const loggingConfig = {
    level: env.LOG_LEVEL,
    format: env.LOG_FORMAT,
} as const;

export const healthCheckConfig = {
    timeout: env.HEALTH_CHECK_TIMEOUT,
} as const;

export const gracefulShutdownConfig = {
    timeout: env.GRACEFUL_SHUTDOWN_TIMEOUT,
} as const;

export const metricsConfig = {
    enabled: env.ENABLE_METRICS,
    port: env.METRICS_PORT,
} as const; 