/**
 * API Types
 * 
 * Type definitions for API requests and responses.
 */

import { type Request, type Response } from 'express';

/**
 * Extended Request interface with authentication and validation data
 */
export interface AuthenticatedRequest extends Request {
    userId?: string;
    requestId?: string;
    user?: { id: string; [key: string]: unknown };
    validatedQuery?: Record<string, unknown>;
    validatedBody?: Record<string, unknown>;
    validatedParams?: Record<string, unknown>;
}

/**
 * Extended Response interface for type safety
 */
export interface TypedResponse<T = unknown> extends Response {
    json: (body: ApiResponse<T>) => this;
}

/**
 * Interface for pagination query parameters
 */
export interface PaginationQuery {
    page?: string;
    limit?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    [key: string]: string | undefined;
}

/**
 * Request interface with typed pagination query
 */
export interface PaginatedRequest extends Omit<AuthenticatedRequest, 'query'> {
    query: PaginationQuery;
}

/**
 * Standard API Response format
 */
export interface ApiResponse<T = unknown> {
    success: boolean;
    message: string;
    data: T;
    error?: string | object;
    timestamp: number;
}

/**
 * Pagination metadata
 */
export interface PaginationMetadata {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}

/**
 * Paginated response format
 */
export interface PaginatedResponse<T = unknown> {
    success: boolean;
    message: string;
    data: T[];
    pagination: PaginationMetadata;
    timestamp: number;
}

/**
 * Error response format
 */
export interface ErrorResponse {
    success: false;
    message: string;
    error?: string | object;
    timestamp: number;
}

/**
 * Health check response
 */
export interface HealthResponse {
    status: 'healthy' | 'unhealthy';
    timestamp: number;
    uptime: number;
    version?: string;
    checks?: {
        database?: ServiceHealth;
        redis?: ServiceHealth;
        external?: ServiceHealth;
    };
}

/**
 * Service health status
 */
export interface ServiceHealth {
    status: 'healthy' | 'unhealthy';
    responseTime?: number;
    error?: string;
    details?: Record<string, unknown>;
}

/**
 * Metrics response format
 */
export interface MetricsResponse {
    timestamp: string;
    uptime: number;
    memory: {
        used: number;
        total: number;
        percentage: number;
    };
    cpu: {
        usage: number;
    };
    requests: {
        total: number;
        perSecond: number;
        errors: number;
        errorRate: number;
    };
    database: {
        connections: number;
        queries: number;
        avgResponseTime: number;
    };
}

/**
 * Common query parameters
 */
export interface QueryParams {
    page?: string | number;
    limit?: string | number;
    sort?: string;
    order?: 'asc' | 'desc';
    search?: string;
    filter?: string;
}

/**
 * File upload information
 */
export interface FileUploadInfo {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
    filename: string;
    path: string;
}

/**
 * Batch operation result
 */
export interface BatchOperationResult<T = unknown> {
    success: boolean;
    total: number;
    processed: number;
    failed: number;
    results: T[];
    errors: Array<{
        index: number;
        error: string;
    }>;
}

/**
 * API rate limit info
 */
export interface RateLimitInfo {
    limit: number;
    remaining: number;
    reset: number;
    retryAfter?: number;
}

/**
 * Request context information
 */
export interface RequestContext {
    requestId: string;
    userId?: string;
    userAgent?: string;
    ip?: string;
    timestamp: string;
    path: string;
    method: string;
    query?: Record<string, unknown>;
    headers?: Record<string, string>;
}

/**
 * Validation error details
 */
export interface ValidationErrorDetail {
    field: string;
    message: string;
    value?: unknown;
    code?: string;
}

/**
 * Validation error response
 */
export interface ValidationErrorResponse extends ErrorResponse {
    error: {
        code: 'VALIDATION_ERROR';
        details: ValidationErrorDetail[];
    };
}

// Cursor-based Pagination Response
export interface CursorPaginatedResponse<T>
    extends ApiResponse<{
        data: T[];
        pagination: {
            limit: number;
            hasNextPage: boolean;
            nextCursor: string | null;
        };
    }> {}

// API Error
export interface ApiError extends Error {
    statusCode: number;
    code?: string;
    details?: unknown;
} 