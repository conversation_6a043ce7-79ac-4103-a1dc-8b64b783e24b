/**
 * Common Type Definitions
 * 
 * Contains common utility types and interfaces.
 */

import type { Request } from 'express';

// Environment types
export interface Environment {
    NODE_ENV: 'development' | 'production' | 'test';
    PORT: number;
    HOST: string;
    DB_HOST: string;
    DB_PORT: number;
    DB_NAME: string;
    DB_USER: string;
    DB_PASSWORD: string;
    DB_SSL: boolean;
    BCRYPT_ROUNDS: number;
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    CORS_ORIGIN: string;
    CORS_CREDENTIALS: boolean;
    LOG_LEVEL: string;
    LOG_FORMAT: string;
    HEALTH_CHECK_TIMEOUT: number;
    GRACEFUL_SHUTDOWN_TIMEOUT: number;
    REDIS_URL?: string;
    REDIS_PASSWORD?: string;
    ENABLE_METRICS: boolean;
    METRICS_PORT: number;
}

// Validation types
export interface ValidationError {
    field: string;
    message: string;
    value?: unknown;
}

export interface ValidationResult<T = unknown> {
    success: boolean;
    data?: T;
    errors?: ValidationError[];
}

/**
 * Validation error details
 */
export interface ValidationErrorDetails {
    field: string;
    message: string;
    code: string;
    received?: unknown;
}

// Logging types
export interface LogContext {
    requestId?: string;
    userId?: string;
    method?: string;
    url?: string;
    statusCode?: number;
    duration?: number;
    userAgent?: string;
    ip?: string;
    host?: string;
    port?: number;
    database?: string;
    processId?: number;
    [key: string]: unknown;
}

// Pagination types
export interface PaginationOptions {
    page?: number;
    limit?: number;
    offset?: number;
}

/**
 * Query parameters for pagination
 */
export interface PaginationQuery {
    page?: string;
    limit?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

/**
 * Paginated result
 */
export interface PaginatedResult<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
    };
}

// Sorting types
export interface SortOptions {
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

// Filtering types
export interface FilterOptions {
    [key: string]: unknown;
}

// Cache types
export interface CacheOptions {
    ttl?: number;
    namespace?: string;
    keyPrefix?: string;
}

// Retry types
export interface RetryOptions {
    maxRetries?: number;
    retryDelay?: number;
    backoffMultiplier?: number;
}

// Async Result types
export type AsyncResult<T, E = Error> = Promise<{
    success: boolean;
    data?: T;
    error?: E;
}>;

/**
 * Extended request interface with user and request context
 */
export interface AuthenticatedRequest extends Request {
    userId?: string;
    requestId?: string;
    user?: {
        id: string;
        email?: string;
        roles?: string[];
    };
}

/**
 * Base entity interface
 */
export interface BaseEntity {
    id: string;
    createdAt: string;
    updatedAt: string;
}

/**
 * Database result wrapper
 */
export interface DatabaseResult<T> {
    success: boolean;
    data?: T;
    error?: string;
    rowCount?: number;
}

/**
 * Service result wrapper
 */
export interface ServiceResult<T> {
    success: boolean;
    data?: T;
    error?: string;
    statusCode?: number;
}

/**
 * Health check status
 */
export type HealthStatus = 'healthy' | 'unhealthy' | 'degraded';

/**
 * Service health information
 */
export interface ServiceHealth {
    status: HealthStatus;
    message?: string;
    lastChecked?: string;
    details?: Record<string, unknown>;
    responseTime?: number;
    error?: string;
}

/**
 * System health information
 */
export interface SystemHealth {
    status: HealthStatus;
    timestamp: string;
    uptime: number;
    services: Record<string, ServiceHealth>;
    version?: string;
}

/**
 * Error details
 */
export interface ErrorDetails {
    code?: string;
    field?: string;
    message: string;
    details?: unknown;
}

/**
 * File upload information
 */
export interface FileUpload {
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    path: string;
    buffer?: Buffer;
}

/**
 * Metric data point
 */
export interface MetricDataPoint {
    timestamp: number;
    value: number;
    tags?: Record<string, string>;
}

/**
 * Performance measurement
 */
export interface PerformanceMeasurement {
    name: string;
    duration: number;
    startTime: number;
    endTime: number;
    metadata?: Record<string, unknown>;
}

// Utility types
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type DeepRequired<T> = {
    [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Nullable<T> = T | null;

// Base Service Interface
export interface BaseService {
    name: string;
    initialize(): Promise<void>;
    destroy(): Promise<void>;
    healthCheck(): Promise<ServiceHealth>;
} 