/**
 * 环境配置
 * 
 * 环境变量解析和验证工具。
 */

import type { Environment } from '@/shared/types/common-types';
import { config as loadEnv } from 'dotenv';

// 根据当前 NODE_ENV 选择性地加载不同的环境文件（.env, .env.development, .env.production 等）
// 如果对应文件不存在，dotenv 会自动忽略。
loadEnv({ path: `.env.${process.env['NODE_ENV'] ?? 'development'}` });
// 兜底再次尝试加载根目录 .env（若前一步已成功则会被忽略）
loadEnv();

/**
 * 解析和验证环境变量
 */
function parseEnvironmentVariables(): Environment {
    const requiredEnvVars = [
        'PORT',
        'HOST',
        'DB_HOST',
        'DB_PORT',
        'DB_NAME',
        'DB_USER',
        'DB_PASSWORD',
    ];

    // 检查必需的环境变量
    const missingEnvVars = requiredEnvVars.filter(varName => {
        const value = process.env[varName];
        return (value ?? '').trim() === '';
    });

    if (missingEnvVars.length > 0) {
        const errorMessage = `Missing required environment variables: ${missingEnvVars.join(', ')}`;
        throw new Error(errorMessage);
    }

    return {
        NODE_ENV: (process.env['NODE_ENV'] ?? 'development') as Environment['NODE_ENV'],
        PORT: parseInt(process.env['PORT'] ?? '3000', 10),
        HOST: process.env['HOST'] ?? 'localhost',
        DB_HOST: process.env['DB_HOST']!,
        DB_PORT: parseInt(process.env['DB_PORT'] ?? '5432', 10),
        DB_NAME: process.env['DB_NAME']!,
        DB_USER: process.env['DB_USER']!,
        DB_PASSWORD: process.env['DB_PASSWORD']!,
        DB_SSL: process.env['DB_SSL'] === 'true',
        BCRYPT_ROUNDS: parseInt(process.env['BCRYPT_ROUNDS'] ?? '10', 10),
        RATE_LIMIT_WINDOW_MS: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] ?? '900000', 10),
        RATE_LIMIT_MAX_REQUESTS: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] ?? '100', 10),
        CORS_ORIGIN: process.env['CORS_ORIGIN'] ?? 'true',
        CORS_CREDENTIALS: process.env['CORS_CREDENTIALS'] === 'false' ? false : true, // 默认允许凭据
        LOG_LEVEL: process.env['LOG_LEVEL'] ?? 'info',
        LOG_FORMAT: process.env['LOG_FORMAT'] ?? 'combined',
        HEALTH_CHECK_TIMEOUT: parseInt(process.env['HEALTH_CHECK_TIMEOUT'] ?? '5000', 10),
        GRACEFUL_SHUTDOWN_TIMEOUT: parseInt(process.env['GRACEFUL_SHUTDOWN_TIMEOUT'] ?? '10000', 10),
        ...(
            process.env['REDIS_URL'] !== undefined
            && process.env['REDIS_URL'].trim() !== ''
            && { REDIS_URL: process.env['REDIS_URL'] }
        ),
        ...(
            process.env['REDIS_PASSWORD'] !== undefined
            && process.env['REDIS_PASSWORD'].trim() !== ''
            && { REDIS_PASSWORD: process.env['REDIS_PASSWORD'] }
        ),
        ENABLE_METRICS: process.env['ENABLE_METRICS'] === 'true',
        METRICS_PORT: parseInt(process.env['METRICS_PORT'] ?? '9090', 10),
    };
}

// 导出解析后的环境变量
export const env = parseEnvironmentVariables();

/**
 * 获取带默认值的环境变量
 */
export function getEnvVar(key: string, defaultValue?: string): string {
    return process.env[key] ?? defaultValue ?? '';
}

/**
 * 获取必需的环境变量（如果未找到则抛出异常）
 */
export function getRequiredEnvVar(key: string): string {
    const value = process.env[key];
    if ((value ?? '').trim() === '') {
        throw new Error(`Required environment variable ${key} is not set`);
    }
    return value ?? '';
}

/**
 * 获取数字类型的环境变量
 */
export function getEnvVarAsNumber(key: string, defaultValue?: number): number {
    const value = process.env[key];
    if ((value ?? '').trim() === '') {
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        throw new Error(`Environment variable ${key} is not set`);
    }

    const parsed = parseInt(value ?? '0', 10);
    if (isNaN(parsed)) {
        throw new Error(`Environment variable ${key} is not a valid number: ${value}`);
    }

    return parsed;
}

/**
 * 获取布尔类型的环境变量
 */
export function getEnvVarAsBoolean(key: string, defaultValue?: boolean): boolean {
    const value = process.env[key];
    if ((value ?? '').trim() === '') {
        if (defaultValue !== undefined) {
            return defaultValue;
        }
        throw new Error(`Environment variable ${key} is not set`);
    }

    return (value ?? '').toLowerCase() === 'true';
}

/**
 * 检查是否在开发环境中运行
 */
export function isDevelopment(): boolean {
    return env.NODE_ENV === 'development';
}

/**
 * 检查是否在生产环境中运行
 */
export function isProduction(): boolean {
    return env.NODE_ENV === 'production';
}

/**
 * 检查是否在测试环境中运行
 */
export function isTest(): boolean {
    return env.NODE_ENV === 'test';
} 