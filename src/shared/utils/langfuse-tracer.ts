/**
 * Langfuse 追踪工具函数
 * 
 * 提供统一的 Langfuse 追踪接口，简化在各个层级的使用
 */

/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { langfuse } from '@/infrastructure/logger/langfuse';
import { logger } from '@/infrastructure/logger';
import type { Request } from 'express';
import type { AuthenticatedRequest, JsonValue, JsonObject, LangfuseTraceMetadata } from '@/shared/types';

/**
 * 追踪上下文接口
 */
export interface TraceContext {
    traceId?: string;
    userId?: string;
    requestId?: string;
    sessionId?: string;
    operation?: string;
    service?: string;
    metadata?: JsonObject;
}

/**
 * 错误追踪接口
 */
export interface ErrorTraceData {
    error: Error;
    context?: TraceContext | undefined;
    statusCode?: number | undefined;
    operation?: string | undefined;
    additionalData?: Record<string, unknown> | undefined;
}

/**
 * 数据库操作追踪接口
 */
export interface DatabaseTraceData {
    operation: string;
    table?: string;
    query?: string;
    duration?: number;
    rowCount?: number;
    context?: TraceContext;
}

/**
 * Langfuse 追踪工具类
 */
export class LangfuseTracer {
    /**
     * 创建操作追踪
     */
    public static createOperationTrace(
        name: string,
        input?: JsonValue,
        context?: TraceContext
    ): unknown {
        if (!langfuse.isEnabled()) {
            return null;
        }

        try {
            const metadata: LangfuseTraceMetadata = {
                service: context?.service ?? 'xui-app-server',
                operation: context?.operation ?? name,
                userId: context?.userId ?? '',
                requestId: context?.requestId ?? '',
                sessionId: context?.sessionId ?? '',
                ...context?.metadata
            };

            return langfuse.createTrace(name, input ?? null, metadata);
        } catch (error) {
            logger.error('Failed to create Langfuse operation trace', { 
                error: error instanceof Error ? error.message : 'Unknown error',
                name,
                context 
            });
            return null;
        }
    }

    /**
     * 记录错误事件
     */
    public static traceError(data: ErrorTraceData): void {
        if (!langfuse.isEnabled()) {
            return;
        }

        try {
            const eventData: JsonObject = {
                errorName: data.error.name,
                errorMessage: data.error.message,
                statusCode: data.statusCode ?? null,
                stack: data.error.stack ?? null,
                ...(data.additionalData ? (data.additionalData as JsonObject) : {})
            };

            const metadata: LangfuseTraceMetadata = {
                service: data.context?.service ?? 'xui-app-server',
                operation: data.operation ?? 'error',
                userId: data.context?.userId ?? '',
                requestId: data.context?.requestId ?? '',
                sessionId: data.context?.sessionId ?? '',
                errorType: data.error.constructor.name,
                timestamp: Date.now(),
                ...data.context?.metadata
            };

            langfuse.createEvent(
                `error.${data.operation ?? 'unknown'}`,
                eventData,
                {
                    error: data.error.message,
                    statusCode: data.statusCode ?? 500
                } as JsonValue,
                metadata
            );
        } catch (error) {
            logger.error('Failed to trace error to Langfuse', { 
                error: error instanceof Error ? error.message : 'Unknown error',
                originalError: data.error.message 
            });
        }
    }

    /**
     * 记录数据库操作事件
     */
    public static traceDatabaseOperation(data: DatabaseTraceData): void {
        if (!langfuse.isEnabled()) {
            return;
        }

        try {
            const eventData: JsonObject = {
                operation: data.operation,
                table: data.table ?? null,
                query: data.query ?? null,
                duration: data.duration ?? null,
                rowCount: data.rowCount ?? null
            };

            const metadata: LangfuseTraceMetadata = {
                service: data.context?.service ?? 'xui-app-server',
                operation: `database.${data.operation}`,
                userId: data.context?.userId ?? '',
                requestId: data.context?.requestId ?? '',
                sessionId: data.context?.sessionId ?? '',
                table: data.table ?? '',
                timestamp: Date.now(),
                ...data.context?.metadata
            };

            langfuse.createEvent(
                `database.${data.operation}`,
                eventData,
                {
                    success: true,
                    duration: data.duration ?? 0,
                    rowCount: data.rowCount ?? 0
                } as JsonValue,
                metadata
            );
        } catch (error) {
            logger.error('Failed to trace database operation to Langfuse', { 
                error: error instanceof Error ? error.message : 'Unknown error',
                operation: data.operation 
            });
        }
    }

    /**
     * 更新追踪结果
     */
    public static updateTrace(
        trace: unknown,
        output?: unknown,
        metadata?: Record<string, unknown>
    ): void {
        if (!langfuse.isEnabled() || trace === null || trace === undefined) {
            return;
        }

        try {
            if (typeof trace === 'object' && 'update' in trace) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (trace as any).update({
                    output,
                    metadata: {
                        ...metadata,
                        updatedAt: Date.now()
                    }
                });
            }
        } catch (error) {
            logger.error('Failed to update Langfuse trace', { 
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * 从请求中提取追踪上下文
     */
    public static extractContextFromRequest(req: Request): TraceContext {
        const authReq = req as AuthenticatedRequest;
        
        return {
            ...(typeof authReq.user?.id === 'string' && authReq.user.id !== ''
                ? { userId: authReq.user.id }
                : {}),
            ...(typeof authReq.requestId === 'string' && authReq.requestId !== ''
                ? { requestId: authReq.requestId }
                : {}),
            ...(typeof req.headers['x-session-id'] === 'string' && req.headers['x-session-id'] !== ''
                ? { sessionId: req.headers['x-session-id'] as string }
                : {}),
            metadata: {
                method: req.method,
                url: req.url,
                userAgent: req.get('User-Agent') ?? null,
                ip: req.ip ?? null
            }
        } as TraceContext;
    }

    /**
     * 创建带有自动错误处理的追踪包装器
     */
    public static wrapWithTrace<T extends unknown[], R>(
        name: string,
        fn: (...args: T) => Promise<R>,
        contextExtractor?: (...args: T) => TraceContext
    ): (...args: T) => Promise<R> {
        return async (...args: T): Promise<R> => {
            const context = contextExtractor ? contextExtractor(...args) : undefined;
            const baseMetadata: LangfuseTraceMetadata = {
                service: context?.service ?? 'xui-app-server',
                operation: context?.operation ?? name,
                userId: context?.userId ?? '',
                requestId: context?.requestId ?? '',
                sessionId: context?.sessionId ?? '',
            };

            const metadata: LangfuseTraceMetadata = {
                ...baseMetadata,
                ...(context?.metadata ?? {})
            };

            const trace = this.createOperationTrace(
                name, 
                { args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)) } as JsonValue,
                { ...context, metadata }
            );
            const startTime = Date.now();

            try {
                const result = await fn(...args);
                const duration = Date.now() - startTime;

                this.updateTrace(trace, { result: JSON.stringify(result) } as JsonValue, {
                    success: true,
                    duration,
                    ...metadata
                });

                return result;
            } catch (error) {
                const duration = Date.now() - startTime;
                const appError = error as Error;

                this.updateTrace(trace, { error: appError.message } as JsonValue, {
                    success: false,
                    duration,
                    error: true,
                    errorMessage: appError.message,
                    ...metadata
                });

                this.traceError({
                    error: appError,
                    context: { ...context, metadata },
                    operation: name
                });

                throw error;
            }
        };
    }

    /**
     * 记录用户反馈评分
     */
    public static scoreTrace(
        trace: unknown,
        name: string,
        value: number,
        comment?: string
    ): void {
        if (!langfuse.isEnabled()) {
            return;
        }

        try {
            if (typeof trace === 'object' && trace !== null && 'score' in trace) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (trace as any).score({ name, value, comment });
            }
        } catch (error) {
            logger.error('Failed to score Langfuse trace', { 
                error: error instanceof Error ? error.message : 'Unknown error',
                name,
                value 
            });
        }
    }
}

/**
 * 便捷的导出函数
 */
export const {
    createOperationTrace,
    traceError,
    traceDatabaseOperation,
    updateTrace,
    extractContextFromRequest,
    wrapWithTrace,
    scoreTrace
} = LangfuseTracer;
