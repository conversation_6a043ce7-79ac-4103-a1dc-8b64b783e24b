// 简单的分页测试脚本
const axios = require('axios');

async function testPagination() {
    const baseUrl = 'http://localhost:3000/api';
    
    // 这里需要替换为实际的sessionId和认证token
    const sessionId = 'your-session-id';
    const token = 'your-auth-token';
    
    try {
        console.log('=== 测试分页功能 ===');
        
        // 第一页
        console.log('\n1. 获取第一页消息...');
        const firstPage = await axios.get(`${baseUrl}/message/session/${sessionId}?limit=2`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        console.log('第一页结果:', {
            count: firstPage.data.data.data.length,
            hasNextPage: firstPage.data.data.pagination.hasNextPage,
            nextCursor: firstPage.data.data.pagination.nextCursor,
            messages: firstPage.data.data.data.map(m => ({
                id: m.dbId,
                content: m.content.substring(0, 50) + '...',
                createdAt: m.createdAt
            }))
        });
        
        // 第二页
        if (firstPage.data.data.pagination.nextCursor) {
            console.log('\n2. 获取第二页消息...');
            const secondPage = await axios.get(`${baseUrl}/message/session/${sessionId}?limit=2&cursor=${firstPage.data.data.pagination.nextCursor}`, {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            console.log('第二页结果:', {
                count: secondPage.data.data.data.length,
                hasNextPage: secondPage.data.data.pagination.hasNextPage,
                nextCursor: secondPage.data.data.pagination.nextCursor,
                messages: secondPage.data.data.data.map(m => ({
                    id: m.dbId,
                    content: m.content.substring(0, 50) + '...',
                    createdAt: m.createdAt
                }))
            });
        } else {
            console.log('\n2. 没有更多页面');
        }
        
    } catch (error) {
        console.error('测试失败:', error.response?.data || error.message);
    }
}

// 运行测试
// testPagination();

console.log('请修改sessionId和token后运行 testPagination() 函数');
